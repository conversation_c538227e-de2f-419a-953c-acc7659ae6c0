{"version": 3, "sources": ["../../../node_modules/hono/dist/compose.js", "../../../node_modules/hono/dist/request/constants.js", "../../../node_modules/hono/dist/utils/body.js", "../../../node_modules/hono/dist/utils/url.js", "../../../node_modules/hono/dist/request.js", "../../../node_modules/hono/dist/utils/html.js", "../../../node_modules/hono/dist/context.js", "../../../node_modules/hono/dist/router.js", "../../../node_modules/hono/dist/utils/constants.js", "../../../node_modules/hono/dist/hono-base.js", "../../../node_modules/hono/dist/router/reg-exp-router/node.js", "../../../node_modules/hono/dist/router/reg-exp-router/trie.js", "../../../node_modules/hono/dist/router/reg-exp-router/router.js", "../../../node_modules/hono/dist/router/smart-router/router.js", "../../../node_modules/hono/dist/router/trie-router/node.js", "../../../node_modules/hono/dist/router/trie-router/router.js", "../../../node_modules/hono/dist/hono.js", "../../../node_modules/hono/dist/middleware/cors/index.js", "../../../node_modules/hono/dist/utils/color.js", "../../../node_modules/hono/dist/middleware/logger/index.js", "../../../node_modules/hono/dist/middleware/pretty-json/index.js", "../../../src/data/kvProvider.js", "../../../src/routes/students.js", "../../../src/routes/classes.js", "../../../src/routes/books.js", "../../../src/routes/genres.js", "../../../src/routes/sessions.js", "../../../src/routes/recommendations.js", "../../../src/routes/settings.js", "../../../src/routes/data.js", "../../../src/worker.js", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-MXbvgj/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-MXbvgj/middleware-loader.entry.ts"], "sourceRoot": "/Users/<USER>/CascadeProjects/Reading manager/.wrangler/tmp/dev-nMnIGC", "sourcesContent": ["// src/compose.ts\nvar compose = (middleware, onError, onNotFound) => {\n  return (context, next) => {\n    let index = -1;\n    return dispatch(0);\n    async function dispatch(i) {\n      if (i <= index) {\n        throw new Error(\"next() called multiple times\");\n      }\n      index = i;\n      let res;\n      let isError = false;\n      let handler;\n      if (middleware[i]) {\n        handler = middleware[i][0][0];\n        context.req.routeIndex = i;\n      } else {\n        handler = i === middleware.length && next || void 0;\n      }\n      if (handler) {\n        try {\n          res = await handler(context, () => dispatch(i + 1));\n        } catch (err) {\n          if (err instanceof Error && onError) {\n            context.error = err;\n            res = await onError(err, context);\n            isError = true;\n          } else {\n            throw err;\n          }\n        }\n      } else {\n        if (context.finalized === false && onNotFound) {\n          res = await onNotFound(context);\n        }\n      }\n      if (res && (context.finalized === false || isError)) {\n        context.res = res;\n      }\n      return context;\n    }\n  };\n};\nexport {\n  compose\n};\n", "// src/request/constants.ts\nvar GET_MATCH_RESULT = Symbol();\nexport {\n  GET_MATCH_RESULT\n};\n", "// src/utils/body.ts\nimport { HonoRequest } from \"../request.js\";\nvar parseBody = async (request, options = /* @__PURE__ */ Object.create(null)) => {\n  const { all = false, dot = false } = options;\n  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;\n  const contentType = headers.get(\"Content-Type\");\n  if (contentType?.startsWith(\"multipart/form-data\") || contentType?.startsWith(\"application/x-www-form-urlencoded\")) {\n    return parseFormData(request, { all, dot });\n  }\n  return {};\n};\nasync function parseFormData(request, options) {\n  const formData = await request.formData();\n  if (formData) {\n    return convertFormDataToBodyData(formData, options);\n  }\n  return {};\n}\nfunction convertFormDataToBodyData(formData, options) {\n  const form = /* @__PURE__ */ Object.create(null);\n  formData.forEach((value, key) => {\n    const shouldParseAllValues = options.all || key.endsWith(\"[]\");\n    if (!shouldParseAllValues) {\n      form[key] = value;\n    } else {\n      handleParsingAllValues(form, key, value);\n    }\n  });\n  if (options.dot) {\n    Object.entries(form).forEach(([key, value]) => {\n      const shouldParseDotValues = key.includes(\".\");\n      if (shouldParseDotValues) {\n        handleParsingNestedValues(form, key, value);\n        delete form[key];\n      }\n    });\n  }\n  return form;\n}\nvar handleParsingAllValues = (form, key, value) => {\n  if (form[key] !== void 0) {\n    if (Array.isArray(form[key])) {\n      ;\n      form[key].push(value);\n    } else {\n      form[key] = [form[key], value];\n    }\n  } else {\n    if (!key.endsWith(\"[]\")) {\n      form[key] = value;\n    } else {\n      form[key] = [value];\n    }\n  }\n};\nvar handleParsingNestedValues = (form, key, value) => {\n  let nestedForm = form;\n  const keys = key.split(\".\");\n  keys.forEach((key2, index) => {\n    if (index === keys.length - 1) {\n      nestedForm[key2] = value;\n    } else {\n      if (!nestedForm[key2] || typeof nestedForm[key2] !== \"object\" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {\n        nestedForm[key2] = /* @__PURE__ */ Object.create(null);\n      }\n      nestedForm = nestedForm[key2];\n    }\n  });\n};\nexport {\n  parseBody\n};\n", "// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label, next) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    const cacheKey = `${label}#${next}`;\n    if (!patternCache[cacheKey]) {\n      if (match[2]) {\n        patternCache[cacheKey] = next && next[0] !== \":\" && next[0] !== \"*\" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];\n      } else {\n        patternCache[cacheKey] = [label, match[1], true];\n      }\n    }\n    return patternCache[cacheKey];\n  }\n  return null;\n};\nvar tryDecode = (str, decoder) => {\n  try {\n    return decoder(str);\n  } catch {\n    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {\n      try {\n        return decoder(match);\n      } catch {\n        return match;\n      }\n    });\n  }\n};\nvar tryDecodeURI = (str) => tryDecode(str, decodeURI);\nvar getPath = (request) => {\n  const url = request.url;\n  const start = url.indexOf(\"/\", url.indexOf(\":\") + 4);\n  let i = start;\n  for (; i < url.length; i++) {\n    const charCode = url.charCodeAt(i);\n    if (charCode === 37) {\n      const queryIndex = url.indexOf(\"?\", i);\n      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);\n      return tryDecodeURI(path.includes(\"%25\") ? path.replace(/%25/g, \"%2525\") : path);\n    } else if (charCode === 63) {\n      break;\n    }\n  }\n  return url.slice(start, i);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result.at(-1) === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (base, sub, ...rest) => {\n  if (rest.length) {\n    sub = mergePath(sub, ...rest);\n  }\n  return `${base?.[0] === \"/\" ? \"\" : \"/\"}${base}${sub === \"/\" ? \"\" : `${base?.at(-1) === \"/\" ? \"\" : \"/\"}${sub?.[0] === \"/\" ? sub.slice(1) : sub}`}`;\n};\nvar checkOptionalParameter = (path) => {\n  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(\":\")) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return value.indexOf(\"%\") !== -1 ? tryDecode(value, decodeURIComponent_) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\nexport {\n  checkOptionalParameter,\n  decodeURIComponent_,\n  getPath,\n  getPathNoStrict,\n  getPattern,\n  getQueryParam,\n  getQueryParams,\n  getQueryStrings,\n  mergePath,\n  splitPath,\n  splitRoutingPath,\n  tryDecode\n};\n", "// src/request.ts\nimport { GET_MATCH_RESULT } from \"./request/constants.js\";\nimport { parseBody } from \"./utils/body.js\";\nimport { decodeURIComponent_, getQueryParam, getQueryParams, tryDecode } from \"./utils/url.js\";\nvar tryDecodeURIComponent = (str) => tryDecode(str, decodeURIComponent_);\nvar HonoRequest = class {\n  raw;\n  #validatedData;\n  #matchResult;\n  routeIndex = 0;\n  path;\n  bodyCache = {};\n  constructor(request, path = \"/\", matchResult = [[]]) {\n    this.raw = request;\n    this.path = path;\n    this.#matchResult = matchResult;\n    this.#validatedData = {};\n  }\n  param(key) {\n    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();\n  }\n  #getDecodedParam(key) {\n    const paramKey = this.#matchResult[0][this.routeIndex][1][key];\n    const param = this.#getParamValue(paramKey);\n    return param && /\\%/.test(param) ? tryDecodeURIComponent(param) : param;\n  }\n  #getAllDecodedParams() {\n    const decoded = {};\n    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);\n    for (const key of keys) {\n      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);\n      if (value !== void 0) {\n        decoded[key] = /\\%/.test(value) ? tryDecodeURIComponent(value) : value;\n      }\n    }\n    return decoded;\n  }\n  #getParamValue(paramKey) {\n    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;\n  }\n  query(key) {\n    return getQueryParam(this.url, key);\n  }\n  queries(key) {\n    return getQueryParams(this.url, key);\n  }\n  header(name) {\n    if (name) {\n      return this.raw.headers.get(name) ?? void 0;\n    }\n    const headerData = {};\n    this.raw.headers.forEach((value, key) => {\n      headerData[key] = value;\n    });\n    return headerData;\n  }\n  async parseBody(options) {\n    return this.bodyCache.parsedBody ??= await parseBody(this, options);\n  }\n  #cachedBody = (key) => {\n    const { bodyCache, raw } = this;\n    const cachedBody = bodyCache[key];\n    if (cachedBody) {\n      return cachedBody;\n    }\n    const anyCachedKey = Object.keys(bodyCache)[0];\n    if (anyCachedKey) {\n      return bodyCache[anyCachedKey].then((body) => {\n        if (anyCachedKey === \"json\") {\n          body = JSON.stringify(body);\n        }\n        return new Response(body)[key]();\n      });\n    }\n    return bodyCache[key] = raw[key]();\n  };\n  json() {\n    return this.#cachedBody(\"text\").then((text) => JSON.parse(text));\n  }\n  text() {\n    return this.#cachedBody(\"text\");\n  }\n  arrayBuffer() {\n    return this.#cachedBody(\"arrayBuffer\");\n  }\n  blob() {\n    return this.#cachedBody(\"blob\");\n  }\n  formData() {\n    return this.#cachedBody(\"formData\");\n  }\n  addValidatedData(target, data) {\n    this.#validatedData[target] = data;\n  }\n  valid(target) {\n    return this.#validatedData[target];\n  }\n  get url() {\n    return this.raw.url;\n  }\n  get method() {\n    return this.raw.method;\n  }\n  get [GET_MATCH_RESULT]() {\n    return this.#matchResult;\n  }\n  get matchedRoutes() {\n    return this.#matchResult[0].map(([[, route]]) => route);\n  }\n  get routePath() {\n    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;\n  }\n};\nexport {\n  HonoRequest\n};\n", "// src/utils/html.ts\nvar HtmlEscapedCallbackPhase = {\n  Stringify: 1,\n  BeforeStream: 2,\n  Stream: 3\n};\nvar raw = (value, callbacks) => {\n  const escapedString = new String(value);\n  escapedString.isEscaped = true;\n  escapedString.callbacks = callbacks;\n  return escapedString;\n};\nvar escapeRe = /[&<>'\"]/;\nvar stringBufferToString = async (buffer, callbacks) => {\n  let str = \"\";\n  callbacks ||= [];\n  const resolvedBuffer = await Promise.all(buffer);\n  for (let i = resolvedBuffer.length - 1; ; i--) {\n    str += resolvedBuffer[i];\n    i--;\n    if (i < 0) {\n      break;\n    }\n    let r = resolvedBuffer[i];\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    const isEscaped = r.isEscaped;\n    r = await (typeof r === \"object\" ? r.toString() : r);\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    if (r.isEscaped ?? isEscaped) {\n      str += r;\n    } else {\n      const buf = [str];\n      escapeToBuffer(r, buf);\n      str = buf[0];\n    }\n  }\n  return raw(str, callbacks);\n};\nvar escapeToBuffer = (str, buffer) => {\n  const match = str.search(escapeRe);\n  if (match === -1) {\n    buffer[0] += str;\n    return;\n  }\n  let escape;\n  let index;\n  let lastIndex = 0;\n  for (index = match; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34:\n        escape = \"&quot;\";\n        break;\n      case 39:\n        escape = \"&#39;\";\n        break;\n      case 38:\n        escape = \"&amp;\";\n        break;\n      case 60:\n        escape = \"&lt;\";\n        break;\n      case 62:\n        escape = \"&gt;\";\n        break;\n      default:\n        continue;\n    }\n    buffer[0] += str.substring(lastIndex, index) + escape;\n    lastIndex = index + 1;\n  }\n  buffer[0] += str.substring(lastIndex, index);\n};\nvar resolveCallbackSync = (str) => {\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return str;\n  }\n  const buffer = [str];\n  const context = {};\n  callbacks.forEach((c) => c({ phase: HtmlEscapedCallbackPhase.Stringify, buffer, context }));\n  return buffer[0];\n};\nvar resolveCallback = async (str, phase, preserveCallbacks, context, buffer) => {\n  if (typeof str === \"object\" && !(str instanceof String)) {\n    if (!(str instanceof Promise)) {\n      str = str.toString();\n    }\n    if (str instanceof Promise) {\n      str = await str;\n    }\n  }\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return Promise.resolve(str);\n  }\n  if (buffer) {\n    buffer[0] += str;\n  } else {\n    buffer = [str];\n  }\n  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(\n    (res) => Promise.all(\n      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))\n    ).then(() => buffer[0])\n  );\n  if (preserveCallbacks) {\n    return raw(await resStr, callbacks);\n  } else {\n    return resStr;\n  }\n};\nexport {\n  HtmlEscapedCallbackPhase,\n  escapeToBuffer,\n  raw,\n  resolveCallback,\n  resolveCallbackSync,\n  stringBufferToString\n};\n", "// src/context.ts\nimport { HonoRequest } from \"./request.js\";\nimport { HtmlEscapedCallbackPhase, resolveCallback } from \"./utils/html.js\";\nvar TEXT_PLAIN = \"text/plain; charset=UTF-8\";\nvar setDefaultContentType = (contentType, headers) => {\n  return {\n    \"Content-Type\": contentType,\n    ...headers\n  };\n};\nvar Context = class {\n  #rawRequest;\n  #req;\n  env = {};\n  #var;\n  finalized = false;\n  error;\n  #status;\n  #executionCtx;\n  #res;\n  #layout;\n  #renderer;\n  #notFoundHandler;\n  #preparedHeaders;\n  #matchResult;\n  #path;\n  constructor(req, options) {\n    this.#rawRequest = req;\n    if (options) {\n      this.#executionCtx = options.executionCtx;\n      this.env = options.env;\n      this.#notFoundHandler = options.notFoundHandler;\n      this.#path = options.path;\n      this.#matchResult = options.matchResult;\n    }\n  }\n  get req() {\n    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);\n    return this.#req;\n  }\n  get event() {\n    if (this.#executionCtx && \"respondWith\" in this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no FetchEvent\");\n    }\n  }\n  get executionCtx() {\n    if (this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no ExecutionContext\");\n    }\n  }\n  get res() {\n    return this.#res ||= new Response(null, {\n      headers: this.#preparedHeaders ??= new Headers()\n    });\n  }\n  set res(_res) {\n    if (this.#res && _res) {\n      _res = new Response(_res.body, _res);\n      for (const [k, v] of this.#res.headers.entries()) {\n        if (k === \"content-type\") {\n          continue;\n        }\n        if (k === \"set-cookie\") {\n          const cookies = this.#res.headers.getSetCookie();\n          _res.headers.delete(\"set-cookie\");\n          for (const cookie of cookies) {\n            _res.headers.append(\"set-cookie\", cookie);\n          }\n        } else {\n          _res.headers.set(k, v);\n        }\n      }\n    }\n    this.#res = _res;\n    this.finalized = true;\n  }\n  render = (...args) => {\n    this.#renderer ??= (content) => this.html(content);\n    return this.#renderer(...args);\n  };\n  setLayout = (layout) => this.#layout = layout;\n  getLayout = () => this.#layout;\n  setRenderer = (renderer) => {\n    this.#renderer = renderer;\n  };\n  header = (name, value, options) => {\n    if (this.finalized) {\n      this.#res = new Response(this.#res.body, this.#res);\n    }\n    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();\n    if (value === void 0) {\n      headers.delete(name);\n    } else if (options?.append) {\n      headers.append(name, value);\n    } else {\n      headers.set(name, value);\n    }\n  };\n  status = (status) => {\n    this.#status = status;\n  };\n  set = (key, value) => {\n    this.#var ??= /* @__PURE__ */ new Map();\n    this.#var.set(key, value);\n  };\n  get = (key) => {\n    return this.#var ? this.#var.get(key) : void 0;\n  };\n  get var() {\n    if (!this.#var) {\n      return {};\n    }\n    return Object.fromEntries(this.#var);\n  }\n  #newResponse(data, arg, headers) {\n    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();\n    if (typeof arg === \"object\" && \"headers\" in arg) {\n      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);\n      for (const [key, value] of argHeaders) {\n        if (key.toLowerCase() === \"set-cookie\") {\n          responseHeaders.append(key, value);\n        } else {\n          responseHeaders.set(key, value);\n        }\n      }\n    }\n    if (headers) {\n      for (const [k, v] of Object.entries(headers)) {\n        if (typeof v === \"string\") {\n          responseHeaders.set(k, v);\n        } else {\n          responseHeaders.delete(k);\n          for (const v2 of v) {\n            responseHeaders.append(k, v2);\n          }\n        }\n      }\n    }\n    const status = typeof arg === \"number\" ? arg : arg?.status ?? this.#status;\n    return new Response(data, { status, headers: responseHeaders });\n  }\n  newResponse = (...args) => this.#newResponse(...args);\n  body = (data, arg, headers) => this.#newResponse(data, arg, headers);\n  text = (text, arg, headers) => {\n    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(\n      text,\n      arg,\n      setDefaultContentType(TEXT_PLAIN, headers)\n    );\n  };\n  json = (object, arg, headers) => {\n    return this.#newResponse(\n      JSON.stringify(object),\n      arg,\n      setDefaultContentType(\"application/json\", headers)\n    );\n  };\n  html = (html, arg, headers) => {\n    const res = (html2) => this.#newResponse(html2, arg, setDefaultContentType(\"text/html; charset=UTF-8\", headers));\n    return typeof html === \"object\" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);\n  };\n  redirect = (location, status) => {\n    const locationString = String(location);\n    this.header(\n      \"Location\",\n      !/[^\\x00-\\xFF]/.test(locationString) ? locationString : encodeURI(locationString)\n    );\n    return this.newResponse(null, status ?? 302);\n  };\n  notFound = () => {\n    this.#notFoundHandler ??= () => new Response();\n    return this.#notFoundHandler(this);\n  };\n};\nexport {\n  Context,\n  TEXT_PLAIN\n};\n", "// src/router.ts\nvar METHOD_NAME_ALL = \"ALL\";\nvar METHOD_NAME_ALL_LOWERCASE = \"all\";\nvar METHODS = [\"get\", \"post\", \"put\", \"delete\", \"options\", \"patch\"];\nvar MESSAGE_MATCHER_IS_ALREADY_BUILT = \"Can not add a route since the matcher is already built.\";\nvar UnsupportedPathError = class extends Error {\n};\nexport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHODS,\n  METHOD_NAME_ALL,\n  METHOD_NAME_ALL_LOWERCASE,\n  UnsupportedPathError\n};\n", "// src/utils/constants.ts\nvar COMPOSED_HANDLER = \"__COMPOSED_HANDLER\";\nexport {\n  COMPOSED_HANDLER\n};\n", "// src/hono-base.ts\nimport { compose } from \"./compose.js\";\nimport { Context } from \"./context.js\";\nimport { METHODS, METHOD_NAME_ALL, METHOD_NAME_ALL_LOWERCASE } from \"./router.js\";\nimport { COMPOSED_HANDLER } from \"./utils/constants.js\";\nimport { getPath, getPathNoStrict, mergePath } from \"./utils/url.js\";\nvar notFoundHandler = (c) => {\n  return c.text(\"404 Not Found\", 404);\n};\nvar errorHandler = (err, c) => {\n  if (\"getResponse\" in err) {\n    const res = err.getResponse();\n    return c.newResponse(res.body, res);\n  }\n  console.error(err);\n  return c.text(\"Internal Server Error\", 500);\n};\nvar Hono = class {\n  get;\n  post;\n  put;\n  delete;\n  options;\n  patch;\n  all;\n  on;\n  use;\n  router;\n  getPath;\n  _basePath = \"/\";\n  #path = \"/\";\n  routes = [];\n  constructor(options = {}) {\n    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];\n    allMethods.forEach((method) => {\n      this[method] = (args1, ...args) => {\n        if (typeof args1 === \"string\") {\n          this.#path = args1;\n        } else {\n          this.#addRoute(method, this.#path, args1);\n        }\n        args.forEach((handler) => {\n          this.#addRoute(method, this.#path, handler);\n        });\n        return this;\n      };\n    });\n    this.on = (method, path, ...handlers) => {\n      for (const p of [path].flat()) {\n        this.#path = p;\n        for (const m of [method].flat()) {\n          handlers.map((handler) => {\n            this.#addRoute(m.toUpperCase(), this.#path, handler);\n          });\n        }\n      }\n      return this;\n    };\n    this.use = (arg1, ...handlers) => {\n      if (typeof arg1 === \"string\") {\n        this.#path = arg1;\n      } else {\n        this.#path = \"*\";\n        handlers.unshift(arg1);\n      }\n      handlers.forEach((handler) => {\n        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);\n      });\n      return this;\n    };\n    const { strict, ...optionsWithoutStrict } = options;\n    Object.assign(this, optionsWithoutStrict);\n    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;\n  }\n  #clone() {\n    const clone = new Hono({\n      router: this.router,\n      getPath: this.getPath\n    });\n    clone.errorHandler = this.errorHandler;\n    clone.#notFoundHandler = this.#notFoundHandler;\n    clone.routes = this.routes;\n    return clone;\n  }\n  #notFoundHandler = notFoundHandler;\n  errorHandler = errorHandler;\n  route(path, app) {\n    const subApp = this.basePath(path);\n    app.routes.map((r) => {\n      let handler;\n      if (app.errorHandler === errorHandler) {\n        handler = r.handler;\n      } else {\n        handler = async (c, next) => (await compose([], app.errorHandler)(c, () => r.handler(c, next))).res;\n        handler[COMPOSED_HANDLER] = r.handler;\n      }\n      subApp.#addRoute(r.method, r.path, handler);\n    });\n    return this;\n  }\n  basePath(path) {\n    const subApp = this.#clone();\n    subApp._basePath = mergePath(this._basePath, path);\n    return subApp;\n  }\n  onError = (handler) => {\n    this.errorHandler = handler;\n    return this;\n  };\n  notFound = (handler) => {\n    this.#notFoundHandler = handler;\n    return this;\n  };\n  mount(path, applicationHandler, options) {\n    let replaceRequest;\n    let optionHandler;\n    if (options) {\n      if (typeof options === \"function\") {\n        optionHandler = options;\n      } else {\n        optionHandler = options.optionHandler;\n        if (options.replaceRequest === false) {\n          replaceRequest = (request) => request;\n        } else {\n          replaceRequest = options.replaceRequest;\n        }\n      }\n    }\n    const getOptions = optionHandler ? (c) => {\n      const options2 = optionHandler(c);\n      return Array.isArray(options2) ? options2 : [options2];\n    } : (c) => {\n      let executionContext = void 0;\n      try {\n        executionContext = c.executionCtx;\n      } catch {\n      }\n      return [c.env, executionContext];\n    };\n    replaceRequest ||= (() => {\n      const mergedPath = mergePath(this._basePath, path);\n      const pathPrefixLength = mergedPath === \"/\" ? 0 : mergedPath.length;\n      return (request) => {\n        const url = new URL(request.url);\n        url.pathname = url.pathname.slice(pathPrefixLength) || \"/\";\n        return new Request(url, request);\n      };\n    })();\n    const handler = async (c, next) => {\n      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));\n      if (res) {\n        return res;\n      }\n      await next();\n    };\n    this.#addRoute(METHOD_NAME_ALL, mergePath(path, \"*\"), handler);\n    return this;\n  }\n  #addRoute(method, path, handler) {\n    method = method.toUpperCase();\n    path = mergePath(this._basePath, path);\n    const r = { basePath: this._basePath, path, method, handler };\n    this.router.add(method, path, [handler, r]);\n    this.routes.push(r);\n  }\n  #handleError(err, c) {\n    if (err instanceof Error) {\n      return this.errorHandler(err, c);\n    }\n    throw err;\n  }\n  #dispatch(request, executionCtx, env, method) {\n    if (method === \"HEAD\") {\n      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, \"GET\")))();\n    }\n    const path = this.getPath(request, { env });\n    const matchResult = this.router.match(method, path);\n    const c = new Context(request, {\n      path,\n      matchResult,\n      env,\n      executionCtx,\n      notFoundHandler: this.#notFoundHandler\n    });\n    if (matchResult[0].length === 1) {\n      let res;\n      try {\n        res = matchResult[0][0][0][0](c, async () => {\n          c.res = await this.#notFoundHandler(c);\n        });\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n      return res instanceof Promise ? res.then(\n        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))\n      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);\n    }\n    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);\n    return (async () => {\n      try {\n        const context = await composed(c);\n        if (!context.finalized) {\n          throw new Error(\n            \"Context is not finalized. Did you forget to return a Response object or `await next()`?\"\n          );\n        }\n        return context.res;\n      } catch (err) {\n        return this.#handleError(err, c);\n      }\n    })();\n  }\n  fetch = (request, ...rest) => {\n    return this.#dispatch(request, rest[1], rest[0], request.method);\n  };\n  request = (input, requestInit, Env, executionCtx) => {\n    if (input instanceof Request) {\n      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);\n    }\n    input = input.toString();\n    return this.fetch(\n      new Request(\n        /^https?:\\/\\//.test(input) ? input : `http://localhost${mergePath(\"/\", input)}`,\n        requestInit\n      ),\n      Env,\n      executionCtx\n    );\n  };\n  fire = () => {\n    addEventListener(\"fetch\", (event) => {\n      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));\n    });\n  };\n};\nexport {\n  Hono as HonoBase\n};\n", "// src/router/reg-exp-router/node.ts\nvar LABEL_REG_EXP_STR = \"[^/]+\";\nvar ONLY_WILDCARD_REG_EXP_STR = \".*\";\nvar TAIL_WILDCARD_REG_EXP_STR = \"(?:|/.*)\";\nvar PATH_ERROR = Symbol();\nvar regExpMetaChars = new Set(\".\\\\+*[^]$()\");\nfunction compareKey(a, b) {\n  if (a.length === 1) {\n    return b.length === 1 ? a < b ? -1 : 1 : -1;\n  }\n  if (b.length === 1) {\n    return 1;\n  }\n  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {\n    return 1;\n  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {\n    return -1;\n  }\n  if (a === LABEL_REG_EXP_STR) {\n    return 1;\n  } else if (b === LABEL_REG_EXP_STR) {\n    return -1;\n  }\n  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;\n}\nvar Node = class {\n  #index;\n  #varIndex;\n  #children = /* @__PURE__ */ Object.create(null);\n  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {\n    if (tokens.length === 0) {\n      if (this.#index !== void 0) {\n        throw PATH_ERROR;\n      }\n      if (pathErrorCheckOnly) {\n        return;\n      }\n      this.#index = index;\n      return;\n    }\n    const [token, ...restTokens] = tokens;\n    const pattern = token === \"*\" ? restTokens.length === 0 ? [\"\", \"\", ONLY_WILDCARD_REG_EXP_STR] : [\"\", \"\", LABEL_REG_EXP_STR] : token === \"/*\" ? [\"\", \"\", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n    let node;\n    if (pattern) {\n      const name = pattern[1];\n      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;\n      if (name && pattern[2]) {\n        if (regexpStr === \".*\") {\n          throw PATH_ERROR;\n        }\n        regexpStr = regexpStr.replace(/^\\((?!\\?:)(?=[^)]+\\)$)/, \"(?:\");\n        if (/\\((?!\\?:)/.test(regexpStr)) {\n          throw PATH_ERROR;\n        }\n      }\n      node = this.#children[regexpStr];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[regexpStr] = new Node();\n        if (name !== \"\") {\n          node.#varIndex = context.varIndex++;\n        }\n      }\n      if (!pathErrorCheckOnly && name !== \"\") {\n        paramMap.push([name, node.#varIndex]);\n      }\n    } else {\n      node = this.#children[token];\n      if (!node) {\n        if (Object.keys(this.#children).some(\n          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.#children[token] = new Node();\n      }\n    }\n    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);\n  }\n  buildRegExpStr() {\n    const childKeys = Object.keys(this.#children).sort(compareKey);\n    const strList = childKeys.map((k) => {\n      const c = this.#children[k];\n      return (typeof c.#varIndex === \"number\" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\\\${k}` : k) + c.buildRegExpStr();\n    });\n    if (typeof this.#index === \"number\") {\n      strList.unshift(`#${this.#index}`);\n    }\n    if (strList.length === 0) {\n      return \"\";\n    }\n    if (strList.length === 1) {\n      return strList[0];\n    }\n    return \"(?:\" + strList.join(\"|\") + \")\";\n  }\n};\nexport {\n  Node,\n  PATH_ERROR\n};\n", "// src/router/reg-exp-router/trie.ts\nimport { Node } from \"./node.js\";\nvar Trie = class {\n  #context = { varIndex: 0 };\n  #root = new Node();\n  insert(path, index, pathErrorCheckOnly) {\n    const paramAssoc = [];\n    const groups = [];\n    for (let i = 0; ; ) {\n      let replaced = false;\n      path = path.replace(/\\{[^}]+\\}/g, (m) => {\n        const mark = `@\\\\${i}`;\n        groups[i] = [mark, m];\n        i++;\n        replaced = true;\n        return mark;\n      });\n      if (!replaced) {\n        break;\n      }\n    }\n    const tokens = path.match(/(?::[^\\/]+)|(?:\\/\\*$)|./g) || [];\n    for (let i = groups.length - 1; i >= 0; i--) {\n      const [mark] = groups[i];\n      for (let j = tokens.length - 1; j >= 0; j--) {\n        if (tokens[j].indexOf(mark) !== -1) {\n          tokens[j] = tokens[j].replace(mark, groups[i][1]);\n          break;\n        }\n      }\n    }\n    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);\n    return paramAssoc;\n  }\n  buildRegExp() {\n    let regexp = this.#root.buildRegExpStr();\n    if (regexp === \"\") {\n      return [/^$/, [], []];\n    }\n    let captureIndex = 0;\n    const indexReplacementMap = [];\n    const paramReplacementMap = [];\n    regexp = regexp.replace(/#(\\d+)|@(\\d+)|\\.\\*\\$/g, (_, handlerIndex, paramIndex) => {\n      if (handlerIndex !== void 0) {\n        indexReplacementMap[++captureIndex] = Number(handlerIndex);\n        return \"$()\";\n      }\n      if (paramIndex !== void 0) {\n        paramReplacementMap[Number(paramIndex)] = ++captureIndex;\n        return \"\";\n      }\n      return \"\";\n    });\n    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];\n  }\n};\nexport {\n  Trie\n};\n", "// src/router/reg-exp-router/router.ts\nimport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHOD_NAME_ALL,\n  UnsupportedPathError\n} from \"../../router.js\";\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { PATH_ERROR } from \"./node.js\";\nimport { Trie } from \"./trie.js\";\nvar emptyParam = [];\nvar nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];\nvar wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\nfunction buildWildcardRegExp(path) {\n  return wildcardRegExpCache[path] ??= new RegExp(\n    path === \"*\" ? \"\" : `^${path.replace(\n      /\\/\\*$|([.\\\\+*[^\\]$()])/g,\n      (_, metaChar) => metaChar ? `\\\\${metaChar}` : \"(?:|/.*)\"\n    )}$`\n  );\n}\nfunction clearWildcardRegExpCache() {\n  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\n}\nfunction buildMatcherFromPreprocessedRoutes(routes) {\n  const trie = new Trie();\n  const handlerData = [];\n  if (routes.length === 0) {\n    return nullMatcher;\n  }\n  const routesWithStaticPathFlag = routes.map(\n    (route) => [!/\\*|\\/:/.test(route[0]), ...route]\n  ).sort(\n    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length\n  );\n  const staticMap = /* @__PURE__ */ Object.create(null);\n  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {\n    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];\n    if (pathErrorCheckOnly) {\n      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];\n    } else {\n      j++;\n    }\n    let paramAssoc;\n    try {\n      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);\n    } catch (e) {\n      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;\n    }\n    if (pathErrorCheckOnly) {\n      continue;\n    }\n    handlerData[j] = handlers.map(([h, paramCount]) => {\n      const paramIndexMap = /* @__PURE__ */ Object.create(null);\n      paramCount -= 1;\n      for (; paramCount >= 0; paramCount--) {\n        const [key, value] = paramAssoc[paramCount];\n        paramIndexMap[key] = value;\n      }\n      return [h, paramIndexMap];\n    });\n  }\n  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();\n  for (let i = 0, len = handlerData.length; i < len; i++) {\n    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {\n      const map = handlerData[i][j]?.[1];\n      if (!map) {\n        continue;\n      }\n      const keys = Object.keys(map);\n      for (let k = 0, len3 = keys.length; k < len3; k++) {\n        map[keys[k]] = paramReplacementMap[map[keys[k]]];\n      }\n    }\n  }\n  const handlerMap = [];\n  for (const i in indexReplacementMap) {\n    handlerMap[i] = handlerData[indexReplacementMap[i]];\n  }\n  return [regexp, handlerMap, staticMap];\n}\nfunction findMiddleware(middleware, path) {\n  if (!middleware) {\n    return void 0;\n  }\n  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {\n    if (buildWildcardRegExp(k).test(path)) {\n      return [...middleware[k]];\n    }\n  }\n  return void 0;\n}\nvar RegExpRouter = class {\n  name = \"RegExpRouter\";\n  #middleware;\n  #routes;\n  constructor() {\n    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n  }\n  add(method, path, handler) {\n    const middleware = this.#middleware;\n    const routes = this.#routes;\n    if (!middleware || !routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    if (!middleware[method]) {\n      ;\n      [middleware, routes].forEach((handlerMap) => {\n        handlerMap[method] = /* @__PURE__ */ Object.create(null);\n        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {\n          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];\n        });\n      });\n    }\n    if (path === \"/*\") {\n      path = \"*\";\n    }\n    const paramCount = (path.match(/\\/:/g) || []).length;\n    if (/\\*$/.test(path)) {\n      const re = buildWildcardRegExp(path);\n      if (method === METHOD_NAME_ALL) {\n        Object.keys(middleware).forEach((m) => {\n          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n        });\n      } else {\n        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n      }\n      Object.keys(middleware).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(middleware[m]).forEach((p) => {\n            re.test(p) && middleware[m][p].push([handler, paramCount]);\n          });\n        }\n      });\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(routes[m]).forEach(\n            (p) => re.test(p) && routes[m][p].push([handler, paramCount])\n          );\n        }\n      });\n      return;\n    }\n    const paths = checkOptionalParameter(path) || [path];\n    for (let i = 0, len = paths.length; i < len; i++) {\n      const path2 = paths[i];\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          routes[m][path2] ||= [\n            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []\n          ];\n          routes[m][path2].push([handler, paramCount - len + i + 1]);\n        }\n      });\n    }\n  }\n  match(method, path) {\n    clearWildcardRegExpCache();\n    const matchers = this.#buildAllMatchers();\n    this.match = (method2, path2) => {\n      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];\n      const staticMatch = matcher[2][path2];\n      if (staticMatch) {\n        return staticMatch;\n      }\n      const match = path2.match(matcher[0]);\n      if (!match) {\n        return [[], emptyParam];\n      }\n      const index = match.indexOf(\"\", 1);\n      return [matcher[1][index], match];\n    };\n    return this.match(method, path);\n  }\n  #buildAllMatchers() {\n    const matchers = /* @__PURE__ */ Object.create(null);\n    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {\n      matchers[method] ||= this.#buildMatcher(method);\n    });\n    this.#middleware = this.#routes = void 0;\n    return matchers;\n  }\n  #buildMatcher(method) {\n    const routes = [];\n    let hasOwnRoute = method === METHOD_NAME_ALL;\n    [this.#middleware, this.#routes].forEach((r) => {\n      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];\n      if (ownRoute.length !== 0) {\n        hasOwnRoute ||= true;\n        routes.push(...ownRoute);\n      } else if (method !== METHOD_NAME_ALL) {\n        routes.push(\n          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])\n        );\n      }\n    });\n    if (!hasOwnRoute) {\n      return null;\n    } else {\n      return buildMatcherFromPreprocessedRoutes(routes);\n    }\n  }\n};\nexport {\n  RegExpRouter\n};\n", "// src/router/smart-router/router.ts\nimport { MESSAGE_MATCHER_IS_ALREADY_BUILT, UnsupportedPathError } from \"../../router.js\";\nvar SmartRouter = class {\n  name = \"SmartRouter\";\n  #routers = [];\n  #routes = [];\n  constructor(init) {\n    this.#routers = init.routers;\n  }\n  add(method, path, handler) {\n    if (!this.#routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    this.#routes.push([method, path, handler]);\n  }\n  match(method, path) {\n    if (!this.#routes) {\n      throw new Error(\"Fatal error\");\n    }\n    const routers = this.#routers;\n    const routes = this.#routes;\n    const len = routers.length;\n    let i = 0;\n    let res;\n    for (; i < len; i++) {\n      const router = routers[i];\n      try {\n        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {\n          router.add(...routes[i2]);\n        }\n        res = router.match(method, path);\n      } catch (e) {\n        if (e instanceof UnsupportedPathError) {\n          continue;\n        }\n        throw e;\n      }\n      this.match = router.match.bind(router);\n      this.#routers = [router];\n      this.#routes = void 0;\n      break;\n    }\n    if (i === len) {\n      throw new Error(\"Fatal error\");\n    }\n    this.name = `SmartRouter + ${this.activeRouter.name}`;\n    return res;\n  }\n  get activeRouter() {\n    if (this.#routes || this.#routers.length !== 1) {\n      throw new Error(\"No active router has been determined yet.\");\n    }\n    return this.#routers[0];\n  }\n};\nexport {\n  SmartRouter\n};\n", "// src/router/trie-router/node.ts\nimport { METHOD_NAME_ALL } from \"../../router.js\";\nimport { getPattern, splitPath, splitRoutingPath } from \"../../utils/url.js\";\nvar emptyParams = /* @__PURE__ */ Object.create(null);\nvar Node = class {\n  #methods;\n  #children;\n  #patterns;\n  #order = 0;\n  #params = emptyParams;\n  constructor(method, handler, children) {\n    this.#children = children || /* @__PURE__ */ Object.create(null);\n    this.#methods = [];\n    if (method && handler) {\n      const m = /* @__PURE__ */ Object.create(null);\n      m[method] = { handler, possibleKeys: [], score: 0 };\n      this.#methods = [m];\n    }\n    this.#patterns = [];\n  }\n  insert(method, path, handler) {\n    this.#order = ++this.#order;\n    let curNode = this;\n    const parts = splitRoutingPath(path);\n    const possibleKeys = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const p = parts[i];\n      const nextP = parts[i + 1];\n      const pattern = getPattern(p, nextP);\n      const key = Array.isArray(pattern) ? pattern[0] : p;\n      if (key in curNode.#children) {\n        curNode = curNode.#children[key];\n        if (pattern) {\n          possibleKeys.push(pattern[1]);\n        }\n        continue;\n      }\n      curNode.#children[key] = new Node();\n      if (pattern) {\n        curNode.#patterns.push(pattern);\n        possibleKeys.push(pattern[1]);\n      }\n      curNode = curNode.#children[key];\n    }\n    curNode.#methods.push({\n      [method]: {\n        handler,\n        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),\n        score: this.#order\n      }\n    });\n    return curNode;\n  }\n  #getHandlerSets(node, method, nodeParams, params) {\n    const handlerSets = [];\n    for (let i = 0, len = node.#methods.length; i < len; i++) {\n      const m = node.#methods[i];\n      const handlerSet = m[method] || m[METHOD_NAME_ALL];\n      const processedSet = {};\n      if (handlerSet !== void 0) {\n        handlerSet.params = /* @__PURE__ */ Object.create(null);\n        handlerSets.push(handlerSet);\n        if (nodeParams !== emptyParams || params && params !== emptyParams) {\n          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {\n            const key = handlerSet.possibleKeys[i2];\n            const processed = processedSet[handlerSet.score];\n            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];\n            processedSet[handlerSet.score] = true;\n          }\n        }\n      }\n    }\n    return handlerSets;\n  }\n  search(method, path) {\n    const handlerSets = [];\n    this.#params = emptyParams;\n    const curNode = this;\n    let curNodes = [curNode];\n    const parts = splitPath(path);\n    const curNodesQueue = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const part = parts[i];\n      const isLast = i === len - 1;\n      const tempNodes = [];\n      for (let j = 0, len2 = curNodes.length; j < len2; j++) {\n        const node = curNodes[j];\n        const nextNode = node.#children[part];\n        if (nextNode) {\n          nextNode.#params = node.#params;\n          if (isLast) {\n            if (nextNode.#children[\"*\"]) {\n              handlerSets.push(\n                ...this.#getHandlerSets(nextNode.#children[\"*\"], method, node.#params)\n              );\n            }\n            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));\n          } else {\n            tempNodes.push(nextNode);\n          }\n        }\n        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {\n          const pattern = node.#patterns[k];\n          const params = node.#params === emptyParams ? {} : { ...node.#params };\n          if (pattern === \"*\") {\n            const astNode = node.#children[\"*\"];\n            if (astNode) {\n              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));\n              astNode.#params = params;\n              tempNodes.push(astNode);\n            }\n            continue;\n          }\n          const [key, name, matcher] = pattern;\n          if (!part && !(matcher instanceof RegExp)) {\n            continue;\n          }\n          const child = node.#children[key];\n          const restPathString = parts.slice(i).join(\"/\");\n          if (matcher instanceof RegExp) {\n            const m = matcher.exec(restPathString);\n            if (m) {\n              params[name] = m[0];\n              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));\n              if (Object.keys(child.#children).length) {\n                child.#params = params;\n                const componentCount = m[0].match(/\\//)?.length ?? 0;\n                const targetCurNodes = curNodesQueue[componentCount] ||= [];\n                targetCurNodes.push(child);\n              }\n              continue;\n            }\n          }\n          if (matcher === true || matcher.test(part)) {\n            params[name] = part;\n            if (isLast) {\n              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));\n              if (child.#children[\"*\"]) {\n                handlerSets.push(\n                  ...this.#getHandlerSets(child.#children[\"*\"], method, params, node.#params)\n                );\n              }\n            } else {\n              child.#params = params;\n              tempNodes.push(child);\n            }\n          }\n        }\n      }\n      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);\n    }\n    if (handlerSets.length > 1) {\n      handlerSets.sort((a, b) => {\n        return a.score - b.score;\n      });\n    }\n    return [handlerSets.map(({ handler, params }) => [handler, params])];\n  }\n};\nexport {\n  Node\n};\n", "// src/router/trie-router/router.ts\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { Node } from \"./node.js\";\nvar TrieRouter = class {\n  name = \"TrieRouter\";\n  #node;\n  constructor() {\n    this.#node = new Node();\n  }\n  add(method, path, handler) {\n    const results = checkOptionalParameter(path);\n    if (results) {\n      for (let i = 0, len = results.length; i < len; i++) {\n        this.#node.insert(method, results[i], handler);\n      }\n      return;\n    }\n    this.#node.insert(method, path, handler);\n  }\n  match(method, path) {\n    return this.#node.search(method, path);\n  }\n};\nexport {\n  TrieRouter\n};\n", "// src/hono.ts\nimport { HonoBase } from \"./hono-base.js\";\nimport { RegExpRouter } from \"./router/reg-exp-router/index.js\";\nimport { SmartRouter } from \"./router/smart-router/index.js\";\nimport { TrieRouter } from \"./router/trie-router/index.js\";\nvar Hono = class extends HonoBase {\n  constructor(options = {}) {\n    super(options);\n    this.router = options.router ?? new SmartRouter({\n      routers: [new RegExpRouter(), new TrieRouter()]\n    });\n  }\n};\nexport {\n  Hono\n};\n", "// src/middleware/cors/index.ts\nvar cors = (options) => {\n  const defaults = {\n    origin: \"*\",\n    allowMethods: [\"GET\", \"HEAD\", \"PUT\", \"POST\", \"DELETE\", \"PATCH\"],\n    allowHeaders: [],\n    exposeHeaders: []\n  };\n  const opts = {\n    ...defaults,\n    ...options\n  };\n  const findAllowOrigin = ((optsOrigin) => {\n    if (typeof optsOrigin === \"string\") {\n      if (optsOrigin === \"*\") {\n        return () => optsOrigin;\n      } else {\n        return (origin) => optsOrigin === origin ? origin : null;\n      }\n    } else if (typeof optsOrigin === \"function\") {\n      return optsOrigin;\n    } else {\n      return (origin) => optsOrigin.includes(origin) ? origin : null;\n    }\n  })(opts.origin);\n  const findAllowMethods = ((optsAllowMethods) => {\n    if (typeof optsAllowMethods === \"function\") {\n      return optsAllowMethods;\n    } else if (Array.isArray(optsAllowMethods)) {\n      return () => optsAllowMethods;\n    } else {\n      return () => [];\n    }\n  })(opts.allowMethods);\n  return async function cors2(c, next) {\n    function set(key, value) {\n      c.res.headers.set(key, value);\n    }\n    const allowOrigin = await findAllowOrigin(c.req.header(\"origin\") || \"\", c);\n    if (allowOrigin) {\n      set(\"Access-Control-Allow-Origin\", allowOrigin);\n    }\n    if (opts.origin !== \"*\") {\n      const existingVary = c.req.header(\"Vary\");\n      if (existingVary) {\n        set(\"Vary\", existingVary);\n      } else {\n        set(\"Vary\", \"Origin\");\n      }\n    }\n    if (opts.credentials) {\n      set(\"Access-Control-Allow-Credentials\", \"true\");\n    }\n    if (opts.exposeHeaders?.length) {\n      set(\"Access-Control-Expose-Headers\", opts.exposeHeaders.join(\",\"));\n    }\n    if (c.req.method === \"OPTIONS\") {\n      if (opts.maxAge != null) {\n        set(\"Access-Control-Max-Age\", opts.maxAge.toString());\n      }\n      const allowMethods = await findAllowMethods(c.req.header(\"origin\") || \"\", c);\n      if (allowMethods.length) {\n        set(\"Access-Control-Allow-Methods\", allowMethods.join(\",\"));\n      }\n      let headers = opts.allowHeaders;\n      if (!headers?.length) {\n        const requestHeaders = c.req.header(\"Access-Control-Request-Headers\");\n        if (requestHeaders) {\n          headers = requestHeaders.split(/\\s*,\\s*/);\n        }\n      }\n      if (headers?.length) {\n        set(\"Access-Control-Allow-Headers\", headers.join(\",\"));\n        c.res.headers.append(\"Vary\", \"Access-Control-Request-Headers\");\n      }\n      c.res.headers.delete(\"Content-Length\");\n      c.res.headers.delete(\"Content-Type\");\n      return new Response(null, {\n        headers: c.res.headers,\n        status: 204,\n        statusText: \"No Content\"\n      });\n    }\n    await next();\n  };\n};\nexport {\n  cors\n};\n", "// src/utils/color.ts\nfunction getColorEnabled() {\n  const { process, Deno } = globalThis;\n  const isNoColor = typeof Deno?.noColor === \"boolean\" ? Deno.noColor : process !== void 0 ? \"NO_COLOR\" in process?.env : false;\n  return !isNoColor;\n}\nasync function getColorEnabledAsync() {\n  const { navigator } = globalThis;\n  const cfWorkers = \"cloudflare:workers\";\n  const isNoColor = navigator !== void 0 && navigator.userAgent === \"Cloudflare-Workers\" ? await (async () => {\n    try {\n      return \"NO_COLOR\" in ((await import(cfWorkers)).env ?? {});\n    } catch {\n      return false;\n    }\n  })() : !getColorEnabled();\n  return !isNoColor;\n}\nexport {\n  getColorEnabled,\n  getColorEnabledAsync\n};\n", "// src/middleware/logger/index.ts\nimport { getColorEnabledAsync } from \"../../utils/color.js\";\nvar humanize = (times) => {\n  const [delimiter, separator] = [\",\", \".\"];\n  const orderTimes = times.map((v) => v.replace(/(\\d)(?=(\\d\\d\\d)+(?!\\d))/g, \"$1\" + delimiter));\n  return orderTimes.join(separator);\n};\nvar time = (start) => {\n  const delta = Date.now() - start;\n  return humanize([delta < 1e3 ? delta + \"ms\" : Math.round(delta / 1e3) + \"s\"]);\n};\nvar colorStatus = async (status) => {\n  const colorEnabled = await getColorEnabledAsync();\n  if (colorEnabled) {\n    switch (status / 100 | 0) {\n      case 5:\n        return `\\x1B[31m${status}\\x1B[0m`;\n      case 4:\n        return `\\x1B[33m${status}\\x1B[0m`;\n      case 3:\n        return `\\x1B[36m${status}\\x1B[0m`;\n      case 2:\n        return `\\x1B[32m${status}\\x1B[0m`;\n    }\n  }\n  return `${status}`;\n};\nasync function log(fn, prefix, method, path, status = 0, elapsed) {\n  const out = prefix === \"<--\" /* Incoming */ ? `${prefix} ${method} ${path}` : `${prefix} ${method} ${path} ${await colorStatus(status)} ${elapsed}`;\n  fn(out);\n}\nvar logger = (fn = console.log) => {\n  return async function logger2(c, next) {\n    const { method, url } = c.req;\n    const path = url.slice(url.indexOf(\"/\", 8));\n    await log(fn, \"<--\" /* Incoming */, method, path);\n    const start = Date.now();\n    await next();\n    await log(fn, \"-->\" /* Outgoing */, method, path, c.res.status, time(start));\n  };\n};\nexport {\n  logger\n};\n", "// src/middleware/pretty-json/index.ts\nvar prettyJSON = (options) => {\n  const targetQuery = options?.query ?? \"pretty\";\n  return async function prettyJSON2(c, next) {\n    const pretty = c.req.query(targetQuery) || c.req.query(targetQuery) === \"\";\n    await next();\n    if (pretty && c.res.headers.get(\"Content-Type\")?.startsWith(\"application/json\")) {\n      const obj = await c.res.json();\n      c.res = new Response(JSON.stringify(obj, null, options?.space ?? 2), c.res);\n    }\n  };\n};\nexport {\n  prettyJSON\n};\n", "/**\n * Cloudflare KV Storage Provider\n * Handles all data operations for the Kids Reading Manager application\n */\n\n// ====================\n// Type Definitions\n// ====================\n\n/**\n * @typedef {Object} Student\n * @property {string} id\n * @property {string} name\n * @property {string|null} classId\n * @property {string|null} readingLevel\n * @property {string|null} lastReadDate\n * @property {Object} preferences\n * @property {string[]} preferences.favoriteGenreIds\n * @property {string[]} preferences.likes\n * @property {string[]} preferences.dislikes\n * @property {string[]} preferences.readingFormats\n * @property {ReadingSession[]} readingSessions\n * @property {string} createdAt\n * @property {string} updatedAt\n */\n\n/**\n * @typedef {Object} Class\n * @property {string} id\n * @property {string} name\n * @property {string|null} teacherName\n * @property {string|null} schoolYear\n * @property {boolean} disabled\n * @property {string} createdAt\n * @property {string} updatedAt\n */\n\n/**\n * @typedef {Object} Book\n * @property {string} id\n * @property {string} title\n * @property {string} author\n * @property {string[]} genreIds\n * @property {string|null} readingLevel\n * @property {string|null} ageRange\n */\n\n/**\n * @typedef {Object} Genre\n * @property {string} id\n * @property {string} name\n * @property {string|null} description\n * @property {boolean} isPredefined\n */\n\n/**\n * @typedef {Object} ReadingSession\n * @property {string} id\n * @property {string} date\n * @property {string} bookId\n * @property {string} bookTitle\n * @property {string} author\n * @property {string} assessment\n * @property {string|null} notes\n * @property {'school'|'home'} environment\n * @property {string} studentId\n */\n\n/**\n * @typedef {Object} Settings\n * @property {Object} readingStatusSettings\n * @property {number} readingStatusSettings.recentlyReadDays\n * @property {number} readingStatusSettings.needsAttentionDays\n */\n\n// ====================\n// UUID Generation Utility\n// ====================\n\n/**\n * Generates a UUID v4 using Web Crypto API\n * @returns {string} UUID v4 string\n */\nexport function generateId() {\n  const array = new Uint8Array(16);\n  crypto.getRandomValues(array);\n\n  // Set version (4) and variant bits\n  array[6] = (array[6] & 0x0f) | 0x40;\n  array[8] = (array[8] & 0x3f) | 0x80;\n\n  const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');\n  return `${hex.slice(0,8)}-${hex.slice(8,12)}-${hex.slice(12,16)}-${hex.slice(16,20)}-${hex.slice(20,32)}`;\n}\n\n// ====================\n// Storage Key Utilities\n// ====================\n\nconst KEY_PREFIXES = {\n  students: 'student:',\n  classes: 'class:',\n  books: 'book:',\n  genres: 'genre:',\n  sessions: 'session:',\n  settings: 'settings',\n  indexes: 'index:'\n};\n\n/**\n * Generates a storage key for a given entity type and ID\n * @param {string} type - Entity type (students, classes, books, etc.)\n * @param {string} id - Entity ID\n * @returns {string} Storage key\n */\nfunction makeKey(type, id) {\n  const prefix = KEY_PREFIXES[type];\n  if (!prefix) {\n    throw new Error(`Unknown entity type: ${type}`);\n  }\n  return `${prefix}${id}`;\n}\n\n// ====================\n// Index Management\n// ====================\n\n/**\n * Updates an index with a new entity ID\n * @param {KVNamespace} kv - KV namespace\n * @param {string} type - Entity type\n * @param {string} id - Entity ID to add\n */\nasync function updateIndex(kv, type, id) {\n  const indexKey = `index:${type}`;\n  const existing = await kv.get(indexKey);\n  const ids = existing ? JSON.parse(existing) : [];\n\n  if (!ids.includes(id)) {\n    ids.push(id);\n    await kv.put(indexKey, JSON.stringify(ids));\n  }\n}\n\n/**\n * Removes an ID from an index\n * @param {KVNamespace} kv - KV namespace\n * @param {string} type - Entity type\n * @param {string} id - Entity ID to remove\n */\nasync function removeFromIndex(kv, type, id) {\n  const indexKey = `index:${type}`;\n  const existing = await kv.get(indexKey);\n  if (!existing) return;\n\n  const ids = JSON.parse(existing).filter(existingId => existingId !== id);\n  await kv.put(indexKey, JSON.stringify(ids));\n}\n\n// ====================\n// Student Operations\n// ====================\n\n/**\n * Gets all students from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @returns {Promise<Student[]>}\n */\nexport async function getStudents(kv) {\n  const index = await kv.get('index:students');\n  const ids = index ? JSON.parse(index) : [];\n\n  const students = [];\n  for (const id of ids) {\n    const student = await kv.get(makeKey('students', id));\n    if (student) {\n      const parsed = JSON.parse(student);\n      // Ensure readingSessions is properly formatted\n      if (!parsed.readingSessions) {\n        parsed.readingSessions = [];\n      }\n      students.push(parsed);\n    }\n  }\n  return students;\n}\n\n/**\n * Gets a student by ID\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Student ID\n * @returns {Promise<Student|null>}\n */\nexport async function getStudentById(kv, id) {\n  const student = await kv.get(makeKey('students', id));\n  return student ? JSON.parse(student) : null;\n}\n\n/**\n * Saves a student to KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {Student} student - Student to save\n * @returns {Promise<Student>}\n */\nexport async function saveStudent(kv, student) {\n  const now = new Date().toISOString();\n  const studentWithTimestamps = {\n    ...student,\n    updatedAt: now,\n    createdAt: student.createdAt || now\n  };\n\n  await kv.put(makeKey('students', student.id), JSON.stringify(studentWithTimestamps));\n  await updateIndex(kv, 'students', student.id);\n\n  return studentWithTimestamps;\n}\n\n/**\n * Deletes a student from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Student ID to delete\n * @returns {Promise<boolean>}\n */\nexport async function deleteStudent(kv, id) {\n  await kv.delete(makeKey('students', id));\n  await removeFromIndex(kv, 'students', id);\n  return true;\n}\n\n// ====================\n// Class Operations\n// ====================\n\n/**\n * Gets all classes from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @returns {Promise<Class[]>}\n */\nexport async function getClasses(kv) {\n  const index = await kv.get('index:classes');\n  const ids = index ? JSON.parse(index) : [];\n\n  const classes = [];\n  for (const id of ids) {\n    const classData = await kv.get(makeKey('classes', id));\n    if (classData) classes.push(JSON.parse(classData));\n  }\n  return classes;\n}\n\n/**\n * Gets a class by ID\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Class ID\n * @returns {Promise<Class|null>}\n */\nexport async function getClassById(kv, id) {\n  const classData = await kv.get(makeKey('classes', id));\n  return classData ? JSON.parse(classData) : null;\n}\n\n/**\n * Saves a class to KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {Class} classData - Class to save\n * @returns {Promise<Class>}\n */\nexport async function saveClass(kv, classData) {\n  const now = new Date().toISOString();\n  const classWithTimestamps = {\n    ...classData,\n    updatedAt: now,\n    createdAt: classData.createdAt || now\n  };\n\n  await kv.put(makeKey('classes', classData.id), JSON.stringify(classWithTimestamps));\n  await updateIndex(kv, 'classes', classData.id);\n\n  return classWithTimestamps;\n}\n\n/**\n * Deletes a class from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Class ID to delete\n * @returns {Promise<boolean>}\n */\nexport async function deleteClass(kv, id) {\n  await kv.delete(makeKey('classes', id));\n  await removeFromIndex(kv, 'classes', id);\n  return true;\n}\n\n// ====================\n// Book Operations\n// ====================\n\n/**\n * Gets all books from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @returns {Promise<Book[]>}\n */\nexport async function getBooks(kv) {\n  const index = await kv.get('index:books');\n  const ids = index ? JSON.parse(index) : [];\n\n  const books = [];\n  for (const id of ids) {\n    const book = await kv.get(makeKey('books', id));\n    if (book) books.push(JSON.parse(book));\n  }\n  return books;\n}\n\n/**\n * Gets a book by ID\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Book ID\n * @returns {Promise<Book|null>}\n */\nexport async function getBookById(kv, id) {\n  const book = await kv.get(makeKey('books', id));\n  return book ? JSON.parse(book) : null;\n}\n\n/**\n * Saves a book to KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {Book} book - Book to save\n * @returns {Promise<Book>}\n */\nexport async function saveBook(kv, book) {\n  await kv.put(makeKey('books', book.id), JSON.stringify(book));\n  await updateIndex(kv, 'books', book.id);\n  return book;\n}\n\n/**\n * Deletes a book from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Book ID to delete\n * @returns {Promise<boolean>}\n */\nexport async function deleteBook(kv, id) {\n  await kv.delete(makeKey('books', id));\n  await removeFromIndex(kv, 'books', id);\n  return true;\n}\n\n// ====================\n// Genre Operations\n// ====================\n\n/**\n * Gets all genres from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @returns {Promise<Genre[]>}\n */\nexport async function getGenres(kv) {\n  const index = await kv.get('index:genres');\n  const ids = index ? JSON.parse(index) : [];\n\n  const genres = [];\n  for (const id of ids) {\n    const genre = await kv.get(makeKey('genres', id));\n    if (genre) genres.push(JSON.parse(genre));\n  }\n  return genres;\n}\n\n/**\n * Gets a genre by ID\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Genre ID\n * @returns {Promise<Genre|null>}\n */\nexport async function getGenreById(kv, id) {\n  const genre = await kv.get(makeKey('genres', id));\n  return genre ? JSON.parse(genre) : null;\n}\n\n/**\n * Saves a genre to KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {Genre} genre - Genre to save\n * @returns {Promise<Genre>}\n */\nexport async function saveGenre(kv, genre) {\n  await kv.put(makeKey('genres', genre.id), JSON.stringify(genre));\n  await updateIndex(kv, 'genres', genre.id);\n  return genre;\n}\n\n/**\n * Deletes a genre from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Genre ID to delete\n * @returns {Promise<boolean>}\n */\nexport async function deleteGenre(kv, id) {\n  await kv.delete(makeKey('genres', id));\n  await removeFromIndex(kv, 'genres', id);\n  return true;\n}\n\n// ====================\n// Reading Session Operations\n// ====================\n\n/**\n * Gets all reading sessions from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @returns {Promise<ReadingSession[]>}\n */\nexport async function getSessions(kv) {\n  const index = await kv.get('index:sessions');\n  const ids = index ? JSON.parse(index) : [];\n\n  const sessions = [];\n  for (const id of ids) {\n    const session = await kv.get(makeKey('sessions', id));\n    if (session) sessions.push(JSON.parse(session));\n  }\n  return sessions;\n}\n\n/**\n * Gets sessions for a specific student\n * @param {KVNamespace} kv - KV namespace\n * @param {string} studentId - Student ID\n * @returns {Promise<ReadingSession[]>}\n */\nexport async function getSessionsByStudent(kv, studentId) {\n  const sessions = await getSessions(kv);\n  return sessions.filter(session => session.studentId === studentId);\n}\n\n/**\n * Saves a reading session to KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {ReadingSession} session - Session to save\n * @returns {Promise<ReadingSession>}\n */\nexport async function saveSession(kv, session) {\n  await kv.put(makeKey('sessions', session.id), JSON.stringify(session));\n  await updateIndex(kv, 'sessions', session.id);\n\n  // Update student's last read date\n  const student = await getStudentById(kv, session.studentId);\n  if (student) {\n    await saveStudent(kv, { ...student, lastReadDate: session.date });\n  }\n\n  return session;\n}\n\n/**\n * Deletes a reading session from KV storage\n * @param {KVNamespace} kv - KV namespace\n * @param {string} id - Session ID to delete\n * @returns {Promise<boolean>}\n */\nexport async function deleteSession(kv, id) {\n  await kv.delete(makeKey('sessions', id));\n  await removeFromIndex(kv, 'sessions', id);\n  return true;\n}\n\n// ====================\n// Settings Operations\n// ====================\n\n/**\n * Gets application settings\n * @param {KVNamespace} kv - KV namespace\n * @returns {Promise<Settings|null>}\n */\nexport async function getSettings(kv) {\n  const settings = await kv.get('settings');\n  return settings ? JSON.parse(settings) : null;\n}\n\n/**\n * Saves application settings\n * @param {KVNamespace} kv - KV namespace\n * @param {Settings} settings - Settings to save\n * @returns {Promise<Settings>}\n */\nexport async function saveSettings(kv, settings) {\n  await kv.put('settings', JSON.stringify(settings));\n  return settings;\n}\n\n// ====================\n// Default Data Initialization\n// ====================\n\n/**\n * Initializes default genres if none exist\n * @param {KVNamespace} kv - KV namespace\n */\nexport async function initializeDefaultGenres(kv) {\n  const existingGenres = await getGenres(kv);\n  if (existingGenres.length > 0) return;\n\n  const defaultGenres = [\n    { id: generateId(), name: 'Adventure', description: 'Exciting stories with exploration and discovery', isPredefined: true },\n    { id: generateId(), name: 'Fantasy', description: 'Magical worlds and mythical creatures', isPredefined: true },\n    { id: generateId(), name: 'Mystery', description: 'Puzzles, detectives, and suspense', isPredefined: true },\n    { id: generateId(), name: 'Science Fiction', description: 'Future worlds and space exploration', isPredefined: true },\n    { id: generateId(), name: 'Animal Stories', description: 'Stories about animals and their adventures', isPredefined: true },\n    { id: generateId(), name: 'Friendship', description: 'Stories about relationships and social themes', isPredefined: true },\n    { id: generateId(), name: 'Family', description: 'Stories about families and home life', isPredefined: true },\n    { id: generateId(), name: 'School', description: 'Stories about school experiences and learning', isPredefined: true },\n    { id: generateId(), name: 'Sports', description: 'Athletic competitions and teamwork', isPredefined: true },\n    { id: generateId(), name: 'Humor', description: 'Funny stories that make you laugh', isPredefined: true }\n  ];\n\n  for (const genre of defaultGenres) {\n    await saveGenre(kv, genre);\n  }\n}\n\n/**\n * Initializes default settings if none exist\n * @param {KVNamespace} kv - KV namespace\n */\nexport async function initializeDefaultSettings(kv) {\n  const existingSettings = await getSettings(kv);\n  if (existingSettings) return;\n\n  const defaultSettings = {\n    readingStatusSettings: {\n      recentlyReadDays: 7,\n      needsAttentionDays: 14\n    }\n  };\n\n  await saveSettings(kv, defaultSettings);\n}", "/**\n * Student API Routes\n * Handles all student-related operations\n */\n\nimport { Hono } from 'hono';\nimport {\n  getStudents,\n  getStudentById,\n  saveStudent,\n  deleteStudent,\n  generateId\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// GET /api/students - List all students\napp.get('/', async (c) => {\n  try {\n    const students = await getStudents(c.env.READING_ASSISTANT_KV);\n    return c.json({ data: students });\n  } catch (error) {\n    console.error('Error fetching students:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch students'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/students - Create new student\napp.post('/', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { name, classId, readingLevel, preferences } = body;\n\n    // Validation\n    if (!name || typeof name !== 'string' || name.trim().length === 0) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Student name is required'\n        }\n      }, 400);\n    }\n\n    // Check if student with this name already exists\n    const existingStudents = await getStudents(c.env.READING_ASSISTANT_KV);\n    const duplicate = existingStudents.find(s => s.name.toLowerCase() === name.toLowerCase().trim());\n    if (duplicate) {\n      return c.json({\n        error: {\n          code: 'DUPLICATE_ERROR',\n          message: 'A student with this name already exists'\n        }\n      }, 409);\n    }\n\n    const student = {\n      id: generateId(),\n      name: name.trim(),\n      classId: classId || null,\n      readingLevel: readingLevel || null,\n      lastReadDate: null,\n      preferences: {\n        favoriteGenreIds: [],\n        likes: [],\n        dislikes: [],\n        readingFormats: [],\n        ...preferences\n      },\n      readingSessions: [],\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    const savedStudent = await saveStudent(c.env.READING_ASSISTANT_KV, student);\n    return c.json({\n      data: savedStudent,\n      message: 'Student created successfully'\n    }, 201);\n  } catch (error) {\n    console.error('Error creating student:', error);\n    return c.json({\n      error: {\n        code: 'CREATE_ERROR',\n        message: 'Failed to create student'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/students/:id - Get student by ID\napp.get('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const student = await getStudentById(c.env.READING_ASSISTANT_KV, id);\n\n    if (!student) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Student not found'\n        }\n      }, 404);\n    }\n\n    return c.json({ data: student });\n  } catch (error) {\n    console.error('Error fetching student:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch student'\n      }\n    }, 500);\n  }\n});\n\n// PUT /api/students/:id - Update student\napp.put('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const updates = await c.req.json();\n\n    const existingStudent = await getStudentById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingStudent) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Student not found'\n        }\n      }, 404);\n    }\n\n    // Validate name if provided\n    if (updates.name !== undefined) {\n      if (typeof updates.name !== 'string' || updates.name.trim().length === 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Student name must be a non-empty string'\n          }\n        }, 400);\n      }\n\n      // Check for duplicate names (excluding current student)\n      const allStudents = await getStudents(c.env.READING_ASSISTANT_KV);\n      const duplicate = allStudents.find(s =>\n        s.id !== id && s.name.toLowerCase() === updates.name.toLowerCase().trim()\n      );\n      if (duplicate) {\n        return c.json({\n          error: {\n            code: 'DUPLICATE_ERROR',\n            message: 'Another student with this name already exists'\n          }\n        }, 409);\n      }\n      updates.name = updates.name.trim();\n    }\n\n    const updatedStudent = {\n      ...existingStudent,\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n\n    const savedStudent = await saveStudent(c.env.READING_ASSISTANT_KV, updatedStudent);\n    return c.json({\n      data: savedStudent,\n      message: 'Student updated successfully'\n    });\n  } catch (error) {\n    console.error('Error updating student:', error);\n    return c.json({\n      error: {\n        code: 'UPDATE_ERROR',\n        message: 'Failed to update student'\n      }\n    }, 500);\n  }\n});\n\n// DELETE /api/students/:id - Delete student\napp.delete('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n\n    const existingStudent = await getStudentById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingStudent) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Student not found'\n        }\n      }, 404);\n    }\n\n    await deleteStudent(c.env.READING_ASSISTANT_KV, id);\n    return c.json({\n      message: 'Student deleted successfully'\n    });\n  } catch (error) {\n    console.error('Error deleting student:', error);\n    return c.json({\n      error: {\n        code: 'DELETE_ERROR',\n        message: 'Failed to delete student'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/students/bulk - Bulk import students\napp.post('/bulk', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { students } = body;\n\n    if (!Array.isArray(students)) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Students must be an array'\n        }\n      }, 400);\n    }\n\n    const createdStudents = [];\n    const errors = [];\n\n    for (const studentData of students) {\n      try {\n        const student = {\n          id: generateId(),\n          name: studentData.name?.trim(),\n          classId: studentData.classId || null,\n          readingLevel: studentData.readingLevel || null,\n          lastReadDate: null,\n          preferences: {\n            favoriteGenreIds: [],\n            likes: [],\n            dislikes: [],\n            readingFormats: [],\n            ...studentData.preferences\n          },\n          readingSessions: [],\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n\n        // Validation\n        if (!student.name || student.name.length === 0) {\n          errors.push(`Student missing name: ${JSON.stringify(studentData)}`);\n          continue;\n        }\n\n        const savedStudent = await saveStudent(c.env.READING_ASSISTANT_KV, student);\n        createdStudents.push(savedStudent);\n      } catch (error) {\n        errors.push(`Error creating student ${studentData.name}: ${error.message}`);\n      }\n    }\n\n    return c.json({\n      data: {\n        created: createdStudents,\n        errors\n      },\n      message: `Created ${createdStudents.length} students${errors.length > 0 ? `, ${errors.length} errors` : ''}`\n    }, createdStudents.length > 0 ? 201 : 400);\n  } catch (error) {\n    console.error('Error bulk importing students:', error);\n    return c.json({\n      error: {\n        code: 'BULK_IMPORT_ERROR',\n        message: 'Failed to import students'\n      }\n    }, 500);\n  }\n});\n\nexport default app;", "/**\n * Class API Routes\n * Handles all class-related operations\n */\n\nimport { Hono } from 'hono';\nimport {\n  getClasses,\n  getClassById,\n  saveClass,\n  deleteClass,\n  generateId\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// GET /api/classes - List all classes\napp.get('/', async (c) => {\n  try {\n    const classes = await getClasses(c.env.READING_ASSISTANT_KV);\n    return c.json({ data: classes });\n  } catch (error) {\n    console.error('Error fetching classes:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch classes'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/classes - Create new class\napp.post('/', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { name, teacherName, schoolYear } = body;\n\n    // Validation\n    if (!name || typeof name !== 'string' || name.trim().length === 0) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Class name is required'\n        }\n      }, 400);\n    }\n\n    // Check if class with this name already exists\n    const existingClasses = await getClasses(c.env.READING_ASSISTANT_KV);\n    const duplicate = existingClasses.find(cls => cls.name.toLowerCase() === name.toLowerCase().trim());\n    if (duplicate) {\n      return c.json({\n        error: {\n          code: 'DUPLICATE_ERROR',\n          message: 'A class with this name already exists'\n        }\n      }, 409);\n    }\n\n    const classData = {\n      id: generateId(),\n      name: name.trim(),\n      teacherName: teacherName || null,\n      schoolYear: schoolYear || null,\n      disabled: false,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    const savedClass = await saveClass(c.env.READING_ASSISTANT_KV, classData);\n    return c.json({\n      data: savedClass,\n      message: 'Class created successfully'\n    }, 201);\n  } catch (error) {\n    console.error('Error creating class:', error);\n    return c.json({\n      error: {\n        code: 'CREATE_ERROR',\n        message: 'Failed to create class'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/classes/:id - Get class by ID\napp.get('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const classData = await getClassById(c.env.READING_ASSISTANT_KV, id);\n\n    if (!classData) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Class not found'\n        }\n      }, 404);\n    }\n\n    return c.json({ data: classData });\n  } catch (error) {\n    console.error('Error fetching class:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch class'\n      }\n    }, 500);\n  }\n});\n\n// PUT /api/classes/:id - Update class\napp.put('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const updates = await c.req.json();\n\n    const existingClass = await getClassById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingClass) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Class not found'\n        }\n      }, 404);\n    }\n\n    // Validate name if provided\n    if (updates.name !== undefined) {\n      if (typeof updates.name !== 'string' || updates.name.trim().length === 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Class name must be a non-empty string'\n          }\n        }, 400);\n      }\n\n      // Check for duplicate names (excluding current class)\n      const allClasses = await getClasses(c.env.READING_ASSISTANT_KV);\n      const duplicate = allClasses.find(cls =>\n        cls.id !== id && cls.name.toLowerCase() === updates.name.toLowerCase().trim()\n      );\n      if (duplicate) {\n        return c.json({\n          error: {\n            code: 'DUPLICATE_ERROR',\n            message: 'Another class with this name already exists'\n          }\n        }, 409);\n      }\n      updates.name = updates.name.trim();\n    }\n\n    const updatedClass = {\n      ...existingClass,\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n\n    const savedClass = await saveClass(c.env.READING_ASSISTANT_KV, updatedClass);\n    return c.json({\n      data: savedClass,\n      message: 'Class updated successfully'\n    });\n  } catch (error) {\n    console.error('Error updating class:', error);\n    return c.json({\n      error: {\n        code: 'UPDATE_ERROR',\n        message: 'Failed to update class'\n      }\n    }, 500);\n  }\n});\n\n// DELETE /api/classes/:id - Delete class\napp.delete('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n\n    const existingClass = await getClassById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingClass) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Class not found'\n        }\n      }, 404);\n    }\n\n    await deleteClass(c.env.READING_ASSISTANT_KV, id);\n    return c.json({\n      message: 'Class deleted successfully'\n    });\n  } catch (error) {\n    console.error('Error deleting class:', error);\n    return c.json({\n      error: {\n        code: 'DELETE_ERROR',\n        message: 'Failed to delete class'\n      }\n    }, 500);\n  }\n});\n\nexport default app;", "/**\n * Book API Routes\n * Handles all book-related operations\n */\n\nimport { Hono } from 'hono';\nimport {\n  getBooks,\n  getBookById,\n  saveBook,\n  deleteBook,\n  getGenres,\n  generateId\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// GET /api/books - List all books\napp.get('/', async (c) => {\n  try {\n    const books = await getBooks(c.env.READING_ASSISTANT_KV);\n    return c.json({ data: books });\n  } catch (error) {\n    console.error('Error fetching books:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch books'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/books - Create new book\napp.post('/', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { title, author, genreIds, readingLevel, ageRange } = body;\n\n    // Validation\n    if (!title || typeof title !== 'string' || title.trim().length === 0) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Book title is required'\n        }\n      }, 400);\n    }\n\n    // Validate author if provided\n    if (author !== undefined && author !== null) {\n      if (typeof author !== 'string' || author.trim().length === 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Book author must be a non-empty string'\n          }\n        }, 400);\n      }\n    }\n\n    // Check if book with this title and author already exists\n    const existingBooks = await getBooks(c.env.READING_ASSISTANT_KV);\n    const normalizedTitle = title.toLowerCase().trim();\n    const normalizedAuthor = author ? author.toLowerCase().trim() : null;\n\n    const duplicate = existingBooks.find(b => {\n      const bookTitle = b.title.toLowerCase();\n      const bookAuthor = b.author ? b.author.toLowerCase() : null;\n\n      // If both books have authors, check title + author\n      if (normalizedAuthor && bookAuthor) {\n        return bookTitle === normalizedTitle && bookAuthor === normalizedAuthor;\n      }\n      // If neither has author, just check title\n      if (!normalizedAuthor && !bookAuthor) {\n        return bookTitle === normalizedTitle;\n      }\n      // If one has author and other doesn't, they're different\n      return false;\n    });\n\n    if (duplicate) {\n      const duplicateMessage = author\n        ? 'A book with this title and author already exists'\n        : 'A book with this title already exists';\n      return c.json({\n        error: {\n          code: 'DUPLICATE_ERROR',\n          message: duplicateMessage\n        }\n      }, 409);\n    }\n\n    // Validate genre IDs if provided\n    if (genreIds && Array.isArray(genreIds)) {\n      const genres = await getGenres(c.env.READING_ASSISTANT_KV);\n      const validGenreIds = genres.map(g => g.id);\n      const invalidGenres = genreIds.filter(id => !validGenreIds.includes(id));\n\n      if (invalidGenres.length > 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: `Invalid genre IDs: ${invalidGenres.join(', ')}`\n          }\n        }, 400);\n      }\n    }\n\n    const book = {\n      id: generateId(),\n      title: title.trim(),\n      author: author ? author.trim() : null,\n      genreIds: genreIds || [],\n      readingLevel: readingLevel || null,\n      ageRange: ageRange || null\n    };\n\n    const savedBook = await saveBook(c.env.READING_ASSISTANT_KV, book);\n    return c.json({\n      data: savedBook,\n      message: 'Book created successfully'\n    }, 201);\n  } catch (error) {\n    console.error('Error creating book:', error);\n    return c.json({\n      error: {\n        code: 'CREATE_ERROR',\n        message: 'Failed to create book'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/books/:id - Get book by ID\napp.get('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const book = await getBookById(c.env.READING_ASSISTANT_KV, id);\n\n    if (!book) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Book not found'\n        }\n      }, 404);\n    }\n\n    return c.json({ data: book });\n  } catch (error) {\n    console.error('Error fetching book:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch book'\n      }\n    }, 500);\n  }\n});\n\n// PUT /api/books/:id - Update book\napp.put('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const updates = await c.req.json();\n\n    const existingBook = await getBookById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingBook) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Book not found'\n        }\n      }, 404);\n    }\n\n    // Validate title if provided\n    if (updates.title !== undefined) {\n      if (typeof updates.title !== 'string' || updates.title.trim().length === 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Book title must be a non-empty string'\n          }\n        }, 400);\n      }\n      updates.title = updates.title.trim();\n    }\n\n    // Validate author if provided\n    if (updates.author !== undefined) {\n      if (typeof updates.author !== 'string' || updates.author.trim().length === 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Book author must be a non-empty string'\n          }\n        }, 400);\n      }\n      updates.author = updates.author.trim();\n    }\n\n    // Validate title/author combination if both are provided\n    if (updates.title !== undefined && updates.author !== undefined) {\n      const allBooks = await getBooks(c.env.READING_ASSISTANT_KV);\n      const duplicate = allBooks.find(b =>\n        b.id !== id &&\n        b.title.toLowerCase() === updates.title.toLowerCase() &&\n        b.author.toLowerCase() === updates.author.toLowerCase()\n      );\n      if (duplicate) {\n        return c.json({\n          error: {\n            code: 'DUPLICATE_ERROR',\n            message: 'Another book with this title and author already exists'\n          }\n        }, 409);\n      }\n    }\n\n    // Validate genre IDs if provided\n    if (updates.genreIds !== undefined) {\n      if (!Array.isArray(updates.genreIds)) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Genre IDs must be an array'\n          }\n        }, 400);\n      }\n\n      const genres = await getGenres(c.env.READING_ASSISTANT_KV);\n      const validGenreIds = genres.map(g => g.id);\n      const invalidGenres = updates.genreIds.filter(id => !validGenreIds.includes(id));\n\n      if (invalidGenres.length > 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: `Invalid genre IDs: ${invalidGenres.join(', ')}`\n          }\n        }, 400);\n      }\n    }\n\n    const updatedBook = {\n      ...existingBook,\n      ...updates\n    };\n\n    const savedBook = await saveBook(c.env.READING_ASSISTANT_KV, updatedBook);\n    return c.json({\n      data: savedBook,\n      message: 'Book updated successfully'\n    });\n  } catch (error) {\n    console.error('Error updating book:', error);\n    return c.json({\n      error: {\n        code: 'UPDATE_ERROR',\n        message: 'Failed to update book'\n      }\n    }, 500);\n  }\n});\n\n// DELETE /api/books/:id - Delete book\napp.delete('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n\n    const existingBook = await getBookById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingBook) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Book not found'\n        }\n      }, 404);\n    }\n\n    await deleteBook(c.env.READING_ASSISTANT_KV, id);\n    return c.json({\n      message: 'Book deleted successfully'\n    });\n  } catch (error) {\n    console.error('Error deleting book:', error);\n    return c.json({\n      error: {\n        code: 'DELETE_ERROR',\n        message: 'Failed to delete book'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/books/search - Search OpenLibrary for books\napp.get('/search/external', async (c) => {\n  try {\n    const { q: query, limit = 10 } = c.req.query();\n\n    if (!query || query.trim().length === 0) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Search query is required'\n        }\n      }, 400);\n    }\n\n    // Search OpenLibrary API\n    const searchUrl = `https://openlibrary.org/search.json?q=${encodeURIComponent(query.trim())}&limit=${limit}&fields=key,title,author_name,first_publish_year,editions,cover_i,subject`;\n    const response = await fetch(searchUrl);\n\n    if (!response.ok) {\n      return c.json({\n        error: {\n          code: 'EXTERNAL_API_ERROR',\n          message: 'Failed to search OpenLibrary'\n        }\n      }, 500);\n    }\n\n    const data = await response.json();\n\n    // Transform the data to match our book format\n    const books = data.docs.map(doc => ({\n      id: null, // External books don't have our internal ID yet\n      title: doc.title,\n      author: doc.author_name ? doc.author_name.join(', ') : 'Unknown Author',\n      externalId: doc.key,\n      firstPublishYear: doc.first_publish_year,\n      coverImage: doc.cover_i ? `https://covers.openlibrary.org/b/id/${doc.cover_i}-M.jpg` : null,\n      subjects: doc.subject ? doc.subject.slice(0, 5) : [], // Limit to first 5 subjects\n      editionCount: doc.editions ? doc.editions.count || 0 : 0\n    }));\n\n    return c.json({\n      data: books,\n      total: data.num_found,\n      message: `Found ${books.length} books matching \"${query}\"`\n    });\n  } catch (error) {\n    console.error('Error searching OpenLibrary:', error);\n    return c.json({\n      error: {\n        code: 'SEARCH_ERROR',\n        message: 'Failed to search external library'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/books/external/:workId - Get detailed book info from OpenLibrary\napp.get('/external/:workId', async (c) => {\n  try {\n    const { workId } = c.req.param();\n\n    // Remove any prefix like \"/works/\" if present\n    const cleanWorkId = workId.replace('/works/', '');\n\n    // Fetch work data from OpenLibrary\n    const workUrl = `https://openlibrary.org/works/${cleanWorkId}.json`;\n    const workResponse = await fetch(workUrl);\n\n    if (!workResponse.ok) {\n      return c.json({\n        error: {\n          code: 'EXTERNAL_API_ERROR',\n          message: 'Book not found in OpenLibrary'\n        }\n      }, 404);\n    }\n\n    const workData = await workResponse.json();\n\n    // Fetch author data if available\n    let authorData = null;\n    if (workData.authors && workData.authors.length > 0) {\n      const authorKey = workData.authors[0].author?.key || workData.authors[0].key;\n      if (authorKey) {\n        const cleanAuthorKey = authorKey.replace('/authors/', '');\n        const authorUrl = `https://openlibrary.org/authors/${cleanAuthorKey}.json`;\n        const authorResponse = await fetch(authorUrl);\n        if (authorResponse.ok) {\n          authorData = await authorResponse.json();\n        }\n      }\n    }\n\n    // Transform to our format\n    const bookData = {\n      title: workData.title,\n      author: authorData?.name || 'Unknown Author',\n      description: workData.description?.value || workData.description || null,\n      subjects: workData.subjects ? workData.subjects.slice(0, 10) : [],\n      firstPublishYear: workData.first_publish_date ? new Date(workData.first_publish_date).getFullYear() : null,\n      coverImage: workData.covers && workData.covers.length > 0 ? `https://covers.openlibrary.org/b/id/${workData.covers[0]}-L.jpg` : null,\n      externalId: workData.key,\n      authorBio: authorData?.bio?.value || authorData?.bio || null,\n      authorBirthDate: authorData?.birth_date || null,\n      authorDeathDate: authorData?.death_date || null,\n      pageCount: workData.number_of_pages_median || null\n    };\n\n    return c.json({ data: bookData });\n  } catch (error) {\n    console.error('Error fetching book from OpenLibrary:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch book details'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/books/import - Import book from OpenLibrary to local library\napp.post('/import', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { externalId, genreIds, readingLevel, ageRange } = body;\n\n    if (!externalId) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'External ID is required'\n        }\n      }, 400);\n    }\n\n    // Get detailed book data from OpenLibrary\n    const cleanWorkId = externalId.replace('/works/', '');\n    const workUrl = `https://openlibrary.org/works/${cleanWorkId}.json`;\n    const workResponse = await fetch(workUrl);\n\n    if (!workResponse.ok) {\n      return c.json({\n        error: {\n          code: 'EXTERNAL_API_ERROR',\n          message: 'Book not found in OpenLibrary'\n        }\n      }, 404);\n    }\n\n    const workData = await workResponse.json();\n\n    // Get author data\n    let authorData = null;\n    if (workData.authors && workData.authors.length > 0) {\n      const authorKey = workData.authors[0].author?.key || workData.authors[0].key;\n      if (authorKey) {\n        const cleanAuthorKey = authorKey.replace('/authors/', '');\n        const authorUrl = `https://openlibrary.org/authors/${cleanAuthorKey}.json`;\n        const authorResponse = await fetch(authorUrl);\n        if (authorResponse.ok) {\n          authorData = await authorResponse.json();\n        }\n      }\n    }\n\n    // Check if book already exists in our library\n    const existingBooks = await getBooks(c.env.READING_ASSISTANT_KV);\n    const duplicate = existingBooks.find(b =>\n      b.title.toLowerCase() === workData.title.toLowerCase() &&\n      (authorData?.name || 'Unknown Author').toLowerCase() === b.author.toLowerCase()\n    );\n\n    if (duplicate) {\n      return c.json({\n        error: {\n          code: 'DUPLICATE_ERROR',\n          message: 'This book already exists in your library'\n        }\n      }, 409);\n    }\n\n    // Validate genre IDs if provided\n    if (genreIds && Array.isArray(genreIds)) {\n      const genres = await getGenres(c.env.READING_ASSISTANT_KV);\n      const validGenreIds = genres.map(g => g.id);\n      const invalidGenres = genreIds.filter(id => !validGenreIds.includes(id));\n\n      if (invalidGenres.length > 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: `Invalid genre IDs: ${invalidGenres.join(', ')}`\n          }\n        }, 400);\n      }\n    }\n\n    // Create book object\n    const book = {\n      id: generateId(),\n      title: workData.title,\n      author: authorData?.name || 'Unknown Author',\n      genreIds: genreIds || [],\n      readingLevel: readingLevel || null,\n      ageRange: ageRange || null,\n      description: workData.description?.value || workData.description || null,\n      coverImage: workData.covers && workData.covers.length > 0 ? `https://covers.openlibrary.org/b/id/${workData.covers[0]}-L.jpg` : null,\n      firstPublishYear: workData.first_publish_date ? new Date(workData.first_publish_date).getFullYear() : null,\n      externalId: workData.key,\n      importedFrom: 'openlibrary',\n      importedAt: new Date().toISOString()\n    };\n\n    const savedBook = await saveBook(c.env.READING_ASSISTANT_KV, book);\n    return c.json({\n      data: savedBook,\n      message: 'Book imported successfully from OpenLibrary'\n    }, 201);\n  } catch (error) {\n    console.error('Error importing book from OpenLibrary:', error);\n    return c.json({\n      error: {\n        code: 'IMPORT_ERROR',\n        message: 'Failed to import book'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/books/bulk - Bulk import books\napp.post('/bulk', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { books } = body;\n\n    if (!Array.isArray(books)) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Books must be an array'\n        }\n      }, 400);\n    }\n\n    const createdBooks = [];\n    const errors = [];\n\n    // Get valid genres for validation\n    const genres = await getGenres(c.env.READING_ASSISTANT_KV);\n    const validGenreIds = genres.map(g => g.id);\n\n    for (const bookData of books) {\n      try {\n        // Validate genre IDs\n        let genreIds = bookData.genreIds || [];\n        if (genreIds.length > 0) {\n          const invalidGenres = genreIds.filter(id => !validGenreIds.includes(id));\n          if (invalidGenres.length > 0) {\n            errors.push(`Book \"${bookData.title}\" has invalid genre IDs: ${invalidGenres.join(', ')}`);\n            continue;\n          }\n        }\n\n        const book = {\n          id: generateId(),\n          title: bookData.title?.trim(),\n          author: bookData.author?.trim(),\n          genreIds: genreIds,\n          readingLevel: bookData.readingLevel || null,\n          ageRange: bookData.ageRange || null\n        };\n\n        // Validation\n        if (!book.title || book.title.length === 0) {\n          errors.push(`Book missing title: ${JSON.stringify(bookData)}`);\n          continue;\n        }\n\n        if (!book.author || book.author.length === 0) {\n          errors.push(`Book \"${book.title}\" missing author`);\n          continue;\n        }\n\n        const savedBook = await saveBook(c.env.READING_ASSISTANT_KV, book);\n        createdBooks.push(savedBook);\n      } catch (error) {\n        errors.push(`Error creating book \"${bookData.title}\": ${error.message}`);\n      }\n    }\n\n    return c.json({\n      data: {\n        created: createdBooks,\n        errors\n      },\n      message: `Created ${createdBooks.length} books${errors.length > 0 ? `, ${errors.length} errors` : ''}`\n    }, createdBooks.length > 0 ? 201 : 400);\n  } catch (error) {\n    console.error('Error bulk importing books:', error);\n    return c.json({\n      error: {\n        code: 'BULK_IMPORT_ERROR',\n        message: 'Failed to import books'\n      }\n    }, 500);\n  }\n});\n\nexport default app;", "/**\n * Genre API Routes\n * Handles all genre-related operations\n */\n\nimport { Hono } from 'hono';\nimport {\n  getGenres,\n  getGenreById,\n  saveGenre,\n  deleteGenre,\n  generateId\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// GET /api/genres - List all genres\napp.get('/', async (c) => {\n  try {\n    const genres = await getGenres(c.env.READING_ASSISTANT_KV);\n    return c.json({ data: genres });\n  } catch (error) {\n    console.error('Error fetching genres:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch genres'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/genres - Create new genre\napp.post('/', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { name, description, isPredefined } = body;\n\n    // Validation\n    if (!name || typeof name !== 'string' || name.trim().length === 0) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Genre name is required'\n        }\n      }, 400);\n    }\n\n    // Check if genre with this name already exists\n    const existingGenres = await getGenres(c.env.READING_ASSISTANT_KV);\n    const duplicate = existingGenres.find(g => g.name.toLowerCase() === name.toLowerCase().trim());\n    if (duplicate) {\n      return c.json({\n        error: {\n          code: 'DUPLICATE_ERROR',\n          message: 'A genre with this name already exists'\n        }\n      }, 409);\n    }\n\n    const genre = {\n      id: generateId(),\n      name: name.trim(),\n      description: description || null,\n      isPredefined: isPredefined || false\n    };\n\n    const savedGenre = await saveGenre(c.env.READING_ASSISTANT_KV, genre);\n    return c.json({\n      data: savedGenre,\n      message: 'Genre created successfully'\n    }, 201);\n  } catch (error) {\n    console.error('Error creating genre:', error);\n    return c.json({\n      error: {\n        code: 'CREATE_ERROR',\n        message: 'Failed to create genre'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/genres/:id - Get genre by ID\napp.get('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const genre = await getGenreById(c.env.READING_ASSISTANT_KV, id);\n\n    if (!genre) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Genre not found'\n        }\n      }, 404);\n    }\n\n    return c.json({ data: genre });\n  } catch (error) {\n    console.error('Error fetching genre:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch genre'\n      }\n    }, 500);\n  }\n});\n\n// PUT /api/genres/:id - Update genre\napp.put('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const updates = await c.req.json();\n\n    const existingGenre = await getGenreById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingGenre) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Genre not found'\n        }\n      }, 404);\n    }\n\n    // Don't allow editing predefined genres\n    if (existingGenre.isPredefined) {\n      return c.json({\n        error: {\n          code: 'FORBIDDEN',\n          message: 'Cannot edit predefined genres'\n        }\n      }, 403);\n    }\n\n    // Validate name if provided\n    if (updates.name !== undefined) {\n      if (typeof updates.name !== 'string' || updates.name.trim().length === 0) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Genre name must be a non-empty string'\n          }\n        }, 400);\n      }\n\n      // Check for duplicate names (excluding current genre)\n      const allGenres = await getGenres(c.env.READING_ASSISTANT_KV);\n      const duplicate = allGenres.find(g =>\n        g.id !== id && g.name.toLowerCase() === updates.name.toLowerCase().trim()\n      );\n      if (duplicate) {\n        return c.json({\n          error: {\n            code: 'DUPLICATE_ERROR',\n            message: 'Another genre with this name already exists'\n          }\n        }, 409);\n      }\n      updates.name = updates.name.trim();\n    }\n\n    const updatedGenre = {\n      ...existingGenre,\n      ...updates\n    };\n\n    const savedGenre = await saveGenre(c.env.READING_ASSISTANT_KV, updatedGenre);\n    return c.json({\n      data: savedGenre,\n      message: 'Genre updated successfully'\n    });\n  } catch (error) {\n    console.error('Error updating genre:', error);\n    return c.json({\n      error: {\n        code: 'UPDATE_ERROR',\n        message: 'Failed to update genre'\n      }\n    }, 500);\n  }\n});\n\n// DELETE /api/genres/:id - Delete genre\napp.delete('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n\n    const existingGenre = await getGenreById(c.env.READING_ASSISTANT_KV, id);\n    if (!existingGenre) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Genre not found'\n        }\n      }, 404);\n    }\n\n    // Don't allow deleting predefined genres\n    if (existingGenre.isPredefined) {\n      return c.json({\n        error: {\n          code: 'FORBIDDEN',\n          message: 'Cannot delete predefined genres'\n        }\n      }, 403);\n    }\n\n    await deleteGenre(c.env.READING_ASSISTANT_KV, id);\n    return c.json({\n      message: 'Genre deleted successfully'\n    });\n  } catch (error) {\n    console.error('Error deleting genre:', error);\n    return c.json({\n      error: {\n        code: 'DELETE_ERROR',\n        message: 'Failed to delete genre'\n      }\n    }, 500);\n  }\n});\n\nexport default app;", "/**\n * Reading Session API Routes\n * Handles all reading session operations\n */\n\nimport { Hono } from 'hono';\nimport {\n  getSessions,\n  getSessionsByStudent,\n  saveSession,\n  deleteSession,\n  getBookById,\n  getStudentById,\n  generateId\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// GET /api/sessions - List all sessions\napp.get('/', async (c) => {\n  try {\n    const sessions = await getSessions(c.env.READING_ASSISTANT_KV);\n    return c.json({ data: sessions });\n  } catch (error) {\n    console.error('Error fetching sessions:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch sessions'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/sessions - Create new session\napp.post('/', async (c) => {\n  try {\n    const body = await c.req.json();\n    const {\n      date,\n      bookId,\n      bookTitle,\n      author,\n      assessment,\n      notes,\n      environment,\n      studentId,\n      bookPreference\n    } = body;\n\n    // Validation\n    if (!date || typeof date !== 'string') {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Session date is required'\n        }\n      }, 400);\n    }\n\n    if (!assessment || typeof assessment !== 'string' || assessment.trim().length === 0) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Session assessment is required'\n        }\n      }, 400);\n    }\n\n    if (!environment || !['school', 'home'].includes(environment)) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Environment must be either \"school\" or \"home\"'\n        }\n      }, 400);\n    }\n\n    // Validate book preference if provided\n    if (bookPreference && !['liked', 'meh', 'disliked'].includes(bookPreference)) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Book preference must be \"liked\", \"meh\", or \"disliked\"'\n        }\n      }, 400);\n    }\n\n    if (!studentId || typeof studentId !== 'string') {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Student ID is required'\n        }\n      }, 400);\n    }\n\n    // Validate student exists\n    const student = await getStudentById(c.env.READING_ASSISTANT_KV, studentId);\n    if (!student) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Student not found'\n        }\n      }, 400);\n    }\n\n    // Validate book if bookId is provided\n    if (bookId) {\n      const book = await getBookById(c.env.READING_ASSISTANT_KV, bookId);\n      if (!book) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Book not found'\n          }\n        }, 400);\n      }\n    }\n\n    // If book details are provided in the request, use them\n    // Otherwise, fetch from the book record\n    let finalBookTitle = bookTitle;\n    let finalAuthor = author;\n\n    if (bookId && !bookTitle) {\n      const book = await getBookById(c.env.READING_ASSISTANT_KV, bookId);\n      if (book) {\n        finalBookTitle = book.title;\n        finalAuthor = book.author;\n      }\n    }\n\n    if (!finalBookTitle || !finalAuthor) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Book title and author are required'\n        }\n      }, 400);\n    }\n\n    const session = {\n      id: generateId(),\n      date,\n      bookId: bookId || null,\n      bookTitle: finalBookTitle,\n      author: finalAuthor,\n      assessment: assessment.trim(),\n      notes: notes ? notes.trim() : null,\n      environment,\n      studentId,\n      bookPreference: bookPreference || null\n    };\n\n    const savedSession = await saveSession(c.env.READING_ASSISTANT_KV, session);\n    return c.json({\n      data: savedSession,\n      message: 'Reading session created successfully'\n    }, 201);\n  } catch (error) {\n    console.error('Error creating session:', error);\n    return c.json({\n      error: {\n        code: 'CREATE_ERROR',\n        message: 'Failed to create reading session'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/sessions/:id - Get session by ID\napp.get('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const sessions = await getSessions(c.env.READING_ASSISTANT_KV);\n    const session = sessions.find(s => s.id === id);\n\n    if (!session) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Session not found'\n        }\n      }, 404);\n    }\n\n    return c.json({ data: session });\n  } catch (error) {\n    console.error('Error fetching session:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch session'\n      }\n    }, 500);\n  }\n});\n\n// PUT /api/sessions/:id - Update session\napp.put('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n    const updates = await c.req.json();\n\n    const sessions = await getSessions(c.env.READING_ASSISTANT_KV);\n    const existingSession = sessions.find(s => s.id === id);\n\n    if (!existingSession) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Session not found'\n        }\n      }, 404);\n    }\n\n    // Validate environment if provided\n    if (updates.environment && !['school', 'home'].includes(updates.environment)) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Environment must be either \"school\" or \"home\"'\n        }\n      }, 400);\n    }\n\n    // Validate student if studentId is being changed\n    if (updates.studentId) {\n      const student = await getStudentById(c.env.READING_ASSISTANT_KV, updates.studentId);\n      if (!student) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Student not found'\n          }\n        }, 400);\n      }\n    }\n\n    // Validate book if bookId is being changed\n    if (updates.bookId) {\n      const book = await getBookById(c.env.READING_ASSISTANT_KV, updates.bookId);\n      if (!book) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Book not found'\n          }\n        }, 400);\n      }\n\n      // Update book details if bookId changed\n      if (book) {\n        updates.bookTitle = book.title;\n        updates.author = book.author;\n      }\n    }\n\n    const updatedSession = {\n      ...existingSession,\n      ...updates\n    };\n\n    // Remove the session from storage and re-add it\n    await deleteSession(c.env.READING_ASSISTANT_KV, id);\n    const savedSession = await saveSession(c.env.READING_ASSISTANT_KV, updatedSession);\n\n    return c.json({\n      data: savedSession,\n      message: 'Session updated successfully'\n    });\n  } catch (error) {\n    console.error('Error updating session:', error);\n    return c.json({\n      error: {\n        code: 'UPDATE_ERROR',\n        message: 'Failed to update session'\n      }\n    }, 500);\n  }\n});\n\n// DELETE /api/sessions/:id - Delete session\napp.delete('/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n\n    const sessions = await getSessions(c.env.READING_ASSISTANT_KV);\n    const existingSession = sessions.find(s => s.id === id);\n\n    if (!existingSession) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Session not found'\n        }\n      }, 404);\n    }\n\n    await deleteSession(c.env.READING_ASSISTANT_KV, id);\n    return c.json({\n      message: 'Session deleted successfully'\n    });\n  } catch (error) {\n    console.error('Error deleting session:', error);\n    return c.json({\n      error: {\n        code: 'DELETE_ERROR',\n        message: 'Failed to delete session'\n      }\n    }, 500);\n  }\n});\n\n// GET /api/sessions/student/:id - Get sessions by student\napp.get('/student/:id', async (c) => {\n  try {\n    const { id } = c.req.param();\n\n    // Validate student exists\n    const student = await getStudentById(c.env.READING_ASSISTANT_KV, id);\n    if (!student) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Student not found'\n        }\n      }, 404);\n    }\n\n    const sessions = await getSessionsByStudent(c.env.READING_ASSISTANT_KV, id);\n    return c.json({ data: sessions });\n  } catch (error) {\n    console.error('Error fetching student sessions:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch student sessions'\n      }\n    }, 500);\n  }\n});\n\nexport default app;", "/**\n * Recommendations API Routes\n * Handles AI-powered book recommendations\n */\n\nimport { Hono } from 'hono';\nimport {\n  getStudentById,\n  getBooks,\n  getGenres,\n  getSessionsByStudent\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// POST /api/recommendations - Get AI book recommendations\napp.post('/', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { studentId, limit = 5 } = body;\n\n    // Validation\n    if (!studentId || typeof studentId !== 'string') {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Student ID is required'\n        }\n      }, 400);\n    }\n\n    // Validate student exists\n    const student = await getStudentById(c.env.READING_ASSISTANT_KV, studentId);\n    if (!student) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Student not found'\n        }\n      }, 404);\n    }\n\n    // Get student's reading history and preferences\n    const sessions = await getSessionsByStudent(c.env.READING_ASSISTANT_KV, studentId);\n    const allBooks = await getBooks(c.env.READING_ASSISTANT_KV);\n    const genres = await getGenres(c.env.READING_ASSISTANT_KV);\n\n    // Get books the student has already read\n    const readBookIds = new Set(sessions.map(session => session.bookId).filter(Boolean));\n\n    // Create a simple recommendation algorithm (since we don't have Anthropic API key in this context)\n    // In a real implementation, you would call the Anthropic Claude API here\n    const recommendations = await generateRecommendations(\n      student,\n      sessions,\n      allBooks,\n      genres,\n      readBookIds,\n      limit\n    );\n\n    return c.json({\n      data: recommendations,\n      message: 'Recommendations generated successfully'\n    });\n  } catch (error) {\n    console.error('Error generating recommendations:', error);\n    return c.json({\n      error: {\n        code: 'RECOMMENDATION_ERROR',\n        message: 'Failed to generate recommendations'\n      }\n    }, 500);\n  }\n});\n\n/**\n * Generates book recommendations for a student\n * This is a simplified algorithm - in production, you would use Anthropic Claude API\n */\nasync function generateRecommendations(student, sessions, allBooks, genres, readBookIds, limit) {\n  const recommendations = [];\n  const studentPreferences = student.preferences || {};\n\n  // Filter out already read books\n  const unreadBooks = allBooks.filter(book => !readBookIds.has(book.id));\n\n  // Score books based on various factors\n  const scoredBooks = unreadBooks.map(book => {\n    let score = 0;\n\n    // Genre preference scoring\n    if (studentPreferences.favoriteGenreIds && studentPreferences.favoriteGenreIds.length > 0) {\n      const matchingGenres = book.genreIds.filter(genreId =>\n        studentPreferences.favoriteGenreIds.includes(genreId)\n      );\n      score += matchingGenres.length * 3; // 3 points per matching favorite genre\n    }\n\n    // Avoid disliked genres\n    if (studentPreferences.dislikes && studentPreferences.dislikes.length > 0) {\n      const dislikedGenres = genres.filter(genre =>\n        studentPreferences.dislikes.some(dislike =>\n          genre.name.toLowerCase().includes(dislike.toLowerCase())\n        )\n      );\n      const dislikedGenreIds = dislikedGenres.map(g => g.id);\n      const hasDislikedGenre = book.genreIds.some(genreId => dislikedGenreIds.includes(genreId));\n      if (hasDislikedGenre) {\n        score -= 5; // Penalty for disliked genres\n      }\n    }\n\n    // Reading level matching (if both have reading levels)\n    if (student.readingLevel && book.readingLevel) {\n      if (student.readingLevel === book.readingLevel) {\n        score += 2; // Bonus for matching reading level\n      }\n    }\n\n    // Age range consideration\n    if (book.ageRange) {\n      // This is a simplified age range check\n      // In a real implementation, you'd parse age ranges more carefully\n      score += 1; // Small bonus for having age range info\n    }\n\n    // Recency bonus for books that haven't been read by many students\n    // This is a simple heuristic\n    score += Math.random() * 2; // Add some randomness to vary recommendations\n\n    return { book, score };\n  });\n\n  // Sort by score and return top recommendations\n  scoredBooks.sort((a, b) => b.score - a.score);\n\n  return scoredBooks.slice(0, limit).map(item => ({\n    book: item.book,\n    score: item.score,\n    reasoning: generateReasoning(student, item.book, genres, studentPreferences)\n  }));\n}\n\n/**\n * Generates human-readable reasoning for a recommendation\n */\nfunction generateReasoning(student, book, genres, preferences) {\n  const reasons = [];\n\n  // Check for favorite genres\n  if (preferences.favoriteGenreIds && preferences.favoriteGenreIds.length > 0) {\n    const bookGenres = genres.filter(g => book.genreIds.includes(g.id));\n    const favoriteGenres = genres.filter(g => preferences.favoriteGenreIds.includes(g.id));\n    const matchingGenres = bookGenres.filter(bg =>\n      favoriteGenres.some(fg => fg.id === bg.id)\n    );\n\n    if (matchingGenres.length > 0) {\n      reasons.push(`Matches favorite genre: ${matchingGenres.map(g => g.name).join(', ')}`);\n    }\n  }\n\n  // Reading level match\n  if (student.readingLevel && book.readingLevel && student.readingLevel === book.readingLevel) {\n    reasons.push(`Matches current reading level: ${student.readingLevel}`);\n  }\n\n  // Age appropriateness\n  if (book.ageRange) {\n    reasons.push(`Age appropriate content`);\n  }\n\n  // Default reason if no specific matches\n  if (reasons.length === 0) {\n    const bookGenres = genres.filter(g => book.genreIds.includes(g.id));\n    if (bookGenres.length > 0) {\n      reasons.push(`Features ${bookGenres.map(g => g.name).join(', ')} genre`);\n    } else {\n      reasons.push('Recommended based on general appeal');\n    }\n  }\n\n  return reasons;\n}\n\nexport default app;", "/**\n * Settings API Routes\n * Handles application settings management\n */\n\nimport { Hono } from 'hono';\nimport {\n  getSettings,\n  saveSettings\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// GET /api/settings - Get application settings\napp.get('/', async (c) => {\n  try {\n    const settings = await getSettings(c.env.READING_ASSISTANT_KV);\n\n    if (!settings) {\n      return c.json({\n        error: {\n          code: 'NOT_FOUND',\n          message: 'Settings not found'\n        }\n      }, 404);\n    }\n\n    return c.json({ data: settings });\n  } catch (error) {\n    console.error('Error fetching settings:', error);\n    return c.json({\n      error: {\n        code: 'FETCH_ERROR',\n        message: 'Failed to fetch settings'\n      }\n    }, 500);\n  }\n});\n\n// PUT /api/settings - Update application settings\napp.put('/', async (c) => {\n  try {\n    const updates = await c.req.json();\n\n    // Get current settings\n    const currentSettings = await getSettings(c.env.READING_ASSISTANT_KV) || {\n      readingStatusSettings: {\n        recentlyReadDays: 7,\n        needsAttentionDays: 14\n      }\n    };\n\n    // Validate readingStatusSettings\n    if (updates.readingStatusSettings) {\n      const { readingStatusSettings } = updates;\n\n      if (readingStatusSettings.recentlyReadDays !== undefined) {\n        const days = parseInt(readingStatusSettings.recentlyReadDays);\n        if (isNaN(days) || days < 1 || days > 365) {\n          return c.json({\n            error: {\n              code: 'VALIDATION_ERROR',\n              message: 'Recently read days must be a number between 1 and 365'\n            }\n          }, 400);\n        }\n        readingStatusSettings.recentlyReadDays = days;\n      }\n\n      if (readingStatusSettings.needsAttentionDays !== undefined) {\n        const days = parseInt(readingStatusSettings.needsAttentionDays);\n        if (isNaN(days) || days < 1 || days > 365) {\n          return c.json({\n            error: {\n              code: 'VALIDATION_ERROR',\n              message: 'Needs attention days must be a number between 1 and 365'\n            }\n          }, 400);\n        }\n        readingStatusSettings.needsAttentionDays = days;\n      }\n\n      // Ensure recentlyReadDays is less than needsAttentionDays\n      if (readingStatusSettings.recentlyReadDays >= readingStatusSettings.needsAttentionDays) {\n        return c.json({\n          error: {\n            code: 'VALIDATION_ERROR',\n            message: 'Recently read days must be less than needs attention days'\n          }\n        }, 400);\n      }\n    }\n\n    const updatedSettings = {\n      ...currentSettings,\n      ...updates,\n      readingStatusSettings: {\n        ...currentSettings.readingStatusSettings,\n        ...updates.readingStatusSettings\n      }\n    };\n\n    const savedSettings = await saveSettings(c.env.READING_ASSISTANT_KV, updatedSettings);\n    return c.json({\n      data: savedSettings,\n      message: 'Settings updated successfully'\n    });\n  } catch (error) {\n    console.error('Error updating settings:', error);\n    return c.json({\n      error: {\n        code: 'UPDATE_ERROR',\n        message: 'Failed to update settings'\n      }\n    }, 500);\n  }\n});\n\nexport default app;", "/**\n * Data Management API Routes\n * Handles data import/export operations\n */\n\nimport { Hono } from 'hono';\nimport {\n  getStudents,\n  getClasses,\n  getBooks,\n  getGenres,\n  getSessions,\n  getSettings,\n  saveStudent,\n  saveClass,\n  saveBook,\n  saveGenre,\n  saveSession,\n  generateId\n} from '../data/kvProvider.js';\n\nconst app = new Hono();\n\n// GET /api/data/export - Export all data as JSON\napp.get('/export', async (c) => {\n  try {\n    const [students, classes, books, genres, sessions, settings] = await Promise.all([\n      getStudents(c.env.READING_ASSISTANT_KV),\n      getClasses(c.env.READING_ASSISTANT_KV),\n      getBooks(c.env.READING_ASSISTANT_KV),\n      getGenres(c.env.READING_ASSISTANT_KV),\n      getSessions(c.env.READING_ASSISTANT_KV),\n      getSettings(c.env.READING_ASSISTANT_KV)\n    ]);\n\n    const exportData = {\n      exportDate: new Date().toISOString(),\n      version: '1.0.0',\n      data: {\n        students,\n        classes,\n        books,\n        genres,\n        sessions,\n        settings: settings || {\n          readingStatusSettings: {\n            recentlyReadDays: 7,\n            needsAttentionDays: 14\n          }\n        }\n      }\n    };\n\n    return c.json({\n      data: exportData,\n      message: 'Data exported successfully'\n    });\n  } catch (error) {\n    console.error('Error exporting data:', error);\n    return c.json({\n      error: {\n        code: 'EXPORT_ERROR',\n        message: 'Failed to export data'\n      }\n    }, 500);\n  }\n});\n\n// POST /api/data/import - Import data from JSON\napp.post('/import', async (c) => {\n  try {\n    const body = await c.req.json();\n    const { data, options = {} } = body;\n\n    if (!data) {\n      return c.json({\n        error: {\n          code: 'VALIDATION_ERROR',\n          message: 'Import data is required'\n        }\n      }, 400);\n    }\n\n    const { overwrite = false } = options;\n    const results = {\n      students: { imported: 0, errors: [] },\n      classes: { imported: 0, errors: [] },\n      books: { imported: 0, errors: [] },\n      genres: { imported: 0, errors: [] },\n      sessions: { imported: 0, errors: [] }\n    };\n\n    // Get existing data to check for conflicts\n    const [existingStudents, existingClasses, existingBooks, existingGenres] = await Promise.all([\n      getStudents(c.env.READING_ASSISTANT_KV),\n      getClasses(c.env.READING_ASSISTANT_KV),\n      getBooks(c.env.READING_ASSISTANT_KV),\n      getGenres(c.env.READING_ASSISTANT_KV)\n    ]);\n\n    const existingStudentNames = new Set(existingStudents.map(s => s.name.toLowerCase()));\n    const existingClassNames = new Set(existingClasses.map(c => c.name.toLowerCase()));\n    const existingBookTitles = new Set(existingBooks.map(b => `${b.title.toLowerCase()}|${b.author.toLowerCase()}`));\n    const existingGenreNames = new Set(existingGenres.map(g => g.name.toLowerCase()));\n\n    // Import genres first (other entities may depend on them)\n    if (data.genres) {\n      for (const genreData of data.genres) {\n        try {\n          if (!genreData.name) {\n            results.genres.errors.push('Genre missing name');\n            continue;\n          }\n\n          const genreName = genreData.name.toLowerCase();\n\n          // Skip if genre already exists and not overwriting\n          if (!overwrite && existingGenreNames.has(genreName)) {\n            results.genres.errors.push(`Genre \"${genreData.name}\" already exists`);\n            continue;\n          }\n\n          const genre = {\n            id: overwrite ? (genreData.id || generateId()) : generateId(),\n            name: genreData.name,\n            description: genreData.description || null,\n            isPredefined: genreData.isPredefined || false\n          };\n\n          await saveGenre(c.env.READING_ASSISTANT_KV, genre);\n          results.genres.imported++;\n        } catch (error) {\n          results.genres.errors.push(`Error importing genre \"${genreData.name}\": ${error.message}`);\n        }\n      }\n    }\n\n    // Import classes\n    if (data.classes) {\n      for (const classData of data.classes) {\n        try {\n          if (!classData.name) {\n            results.classes.errors.push('Class missing name');\n            continue;\n          }\n\n          const className = classData.name.toLowerCase();\n\n          // Skip if class already exists and not overwriting\n          if (!overwrite && existingClassNames.has(className)) {\n            results.classes.errors.push(`Class \"${classData.name}\" already exists`);\n            continue;\n          }\n\n          const classEntity = {\n            id: overwrite ? (classData.id || generateId()) : generateId(),\n            name: classData.name,\n            teacherName: classData.teacherName || null,\n            schoolYear: classData.schoolYear || null,\n            disabled: classData.disabled || false,\n            createdAt: classData.createdAt || new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          };\n\n          await saveClass(c.env.READING_ASSISTANT_KV, classEntity);\n          results.classes.imported++;\n        } catch (error) {\n          results.classes.errors.push(`Error importing class \"${classData.name}\": ${error.message}`);\n        }\n      }\n    }\n\n    // Import books\n    if (data.books) {\n      for (const bookData of data.books) {\n        try {\n          if (!bookData.title || !bookData.author) {\n            results.books.errors.push('Book missing title or author');\n            continue;\n          }\n\n          const bookKey = `${bookData.title.toLowerCase()}|${bookData.author.toLowerCase()}`;\n\n          // Skip if book already exists and not overwriting\n          if (!overwrite && existingBookTitles.has(bookKey)) {\n            results.books.errors.push(`Book \"${bookData.title}\" by ${bookData.author} already exists`);\n            continue;\n          }\n\n          const book = {\n            id: overwrite ? (bookData.id || generateId()) : generateId(),\n            title: bookData.title,\n            author: bookData.author,\n            genreIds: bookData.genreIds || [],\n            readingLevel: bookData.readingLevel || null,\n            ageRange: bookData.ageRange || null\n          };\n\n          await saveBook(c.env.READING_ASSISTANT_KV, book);\n          results.books.imported++;\n        } catch (error) {\n          results.books.errors.push(`Error importing book \"${bookData.title}\": ${error.message}`);\n        }\n      }\n    }\n\n    // Import students\n    if (data.students) {\n      for (const studentData of data.students) {\n        try {\n          if (!studentData.name) {\n            results.students.errors.push('Student missing name');\n            continue;\n          }\n\n          const studentName = studentData.name.toLowerCase();\n\n          // Skip if student already exists and not overwriting\n          if (!overwrite && existingStudentNames.has(studentName)) {\n            results.students.errors.push(`Student \"${studentData.name}\" already exists`);\n            continue;\n          }\n\n          const student = {\n            id: overwrite ? (studentData.id || generateId()) : generateId(),\n            name: studentData.name,\n            classId: studentData.classId || null,\n            readingLevel: studentData.readingLevel || null,\n            lastReadDate: studentData.lastReadDate || null,\n            preferences: {\n              favoriteGenreIds: [],\n              likes: [],\n              dislikes: [],\n              readingFormats: [],\n              ...studentData.preferences\n            },\n            readingSessions: [],\n            createdAt: studentData.createdAt || new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n          };\n\n          await saveStudent(c.env.READING_ASSISTANT_KV, student);\n          results.students.imported++;\n        } catch (error) {\n          results.students.errors.push(`Error importing student \"${studentData.name}\": ${error.message}`);\n        }\n      }\n    }\n\n    // Import sessions\n    if (data.sessions) {\n      for (const sessionData of data.sessions) {\n        try {\n          if (!sessionData.studentId || !sessionData.date || !sessionData.assessment) {\n            results.sessions.errors.push('Session missing required fields');\n            continue;\n          }\n\n          const session = {\n            id: overwrite ? (sessionData.id || generateId()) : generateId(),\n            date: sessionData.date,\n            bookId: sessionData.bookId || null,\n            bookTitle: sessionData.bookTitle,\n            author: sessionData.author,\n            assessment: sessionData.assessment,\n            notes: sessionData.notes || null,\n            environment: sessionData.environment || 'school',\n            studentId: sessionData.studentId\n          };\n\n          await saveSession(c.env.READING_ASSISTANT_KV, session);\n          results.sessions.imported++;\n        } catch (error) {\n          results.sessions.errors.push(`Error importing session: ${error.message}`);\n        }\n      }\n    }\n\n    // Update settings if provided\n    if (data.settings) {\n      try {\n        await saveSettings(c.env.READING_ASSISTANT_KV, data.settings);\n      } catch (error) {\n        console.error('Error importing settings:', error);\n      }\n    }\n\n    const hasErrors = Object.values(results).some(result => result.errors.length > 0);\n    const totalImported = Object.values(results).reduce((sum, result) => sum + result.imported, 0);\n\n    return c.json({\n      data: results,\n      message: `Import completed. Imported ${totalImported} records${hasErrors ? ` with ${Object.values(results).reduce((sum, result) => sum + result.errors.length, 0)} errors` : ''}`\n    }, hasErrors ? 207 : 201);\n  } catch (error) {\n    console.error('Error importing data:', error);\n    return c.json({\n      error: {\n        code: 'IMPORT_ERROR',\n        message: 'Failed to import data'\n      }\n    }, 500);\n  }\n});\n\nexport default app;", "/**\n * Reading Assistant - Cloudflare Worker\n * Clean, simple implementation\n */\n\nimport { Hono } from 'hono';\nimport { cors } from 'hono/cors';\nimport { logger } from 'hono/logger';\nimport { prettyJSON } from 'hono/pretty-json';\n// import { getAssetFromKV } from '@cloudflare/kv-asset-handler';\n\nimport studentRoutes from './routes/students.js';\nimport classRoutes from './routes/classes.js';\nimport bookRoutes from './routes/books.js';\nimport genreRoutes from './routes/genres.js';\nimport sessionRoutes from './routes/sessions.js';\nimport recommendationRoutes from './routes/recommendations.js';\nimport settingsRoutes from './routes/settings.js';\nimport dataRoutes from './routes/data.js';\n\nimport { initializeDefaultGenres, initializeDefaultSettings } from './data/kvProvider.js';\n\nconst app = new Hono();\n\napp.use('*', logger());\napp.use('*', prettyJSON());\napp.use('*', cors({\n  origin: (origin) => origin?.includes('localhost') || origin?.includes('127.0.0.1') ? origin : '*',\n  credentials: true,\n}));\n\napp.get('/health', (c) => c.json({\n  status: 'ok',\n  timestamp: new Date().toISOString(),\n  environment: c.env.ENVIRONMENT || 'development'\n}));\n\napp.route('/api/students', studentRoutes);\napp.route('/api/classes', classRoutes);\napp.route('/api/books', bookRoutes);\napp.route('/api/genres', genreRoutes);\napp.route('/api/sessions', sessionRoutes);\napp.route('/api/recommendations', recommendationRoutes);\napp.route('/api/settings', settingsRoutes);\napp.route('/api/data', dataRoutes);\n\napp.get('*', async (c) => {\n  const url = new URL(c.req.url);\n  const pathname = url.pathname;\n\n  // Handle API routes and health checks\n  if (pathname.startsWith('/api/') || pathname.startsWith('/health')) {\n    return c.notFound();\n  }\n\n  // Try to serve static assets from ASSETS binding\n  try {\n    const assetPath = pathname === '/' ? '/index.html' : pathname;\n    console.log('Looking for asset:', assetPath);\n    \n    const asset = await c.env.ASSETS.fetch(new Request(`https://example.com${assetPath}`));\n    \n    if (asset.ok) {\n      console.log('Serving asset:', assetPath, 'with status:', asset.status);\n      \n      // Clone the response to modify headers\n      const response = new Response(asset.body, {\n        status: asset.status,\n        statusText: asset.statusText,\n        headers: asset.headers\n      });\n      \n      // Ensure proper MIME types for JavaScript files\n      if (pathname.endsWith('.js')) {\n        response.headers.set('Content-Type', 'application/javascript');\n      }\n      \n      // Set cache headers\n      if (pathname === '/' || pathname === '/index.html') {\n        response.headers.set('Cache-Control', 'no-cache');\n      } else {\n        response.headers.set('Cache-Control', 'public, max-age=********');\n      }\n      \n      return response;\n    } else {\n      console.log('Asset not found:', assetPath, 'status:', asset.status);\n    }\n  } catch (error) {\n    console.error('Error serving asset:', error);\n  }\n\n  // If no static asset found and it's not the root, try to serve index.html for SPA routing\n  if (pathname !== '/') {\n    try {\n      const indexAsset = await c.env.ASSETS.fetch(new Request('https://example.com/index.html'));\n      if (indexAsset.ok) {\n        return new Response(indexAsset.body, {\n          headers: {\n            'Content-Type': 'text/html',\n            'Cache-Control': 'no-cache'\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Error serving index.html:', error);\n    }\n  }\n\n  // Fallback HTML if assets are not available\n  return c.html(`<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>Reading Assistant - Primary School Reading Tracker</title>\n    <meta name=\"description\" content=\"A comprehensive reading tracking system for primary school students with AI-powered book recommendations\" />\n    <link rel=\"icon\" type=\"image/x-icon\" href=\"/favicon.ico\" />\n    <!-- Preconnect to fonts -->\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin />\n    <link\n      href=\"https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap\"\n      rel=\"stylesheet\"\n    />\n    <!-- Material Icons -->\n    <link\n      href=\"https://fonts.googleapis.com/icon?family=Material+Icons\"\n      rel=\"stylesheet\"\n    />\n  </head>\n  <body>\n    <div id=\"root\"></div>\n    <!-- Scripts will be injected here by the build process -->\n  <script defer src=\"/static/js/lib-react.fd756841.js\"></script><script defer src=\"/static/js/lib-router.7cc6ab1b.js\"></script><script defer src=\"/static/js/366.42b9c958.js\"></script><script defer src=\"/static/js/index.3ed40cec.js\"></script></body>\n</html>`);\n});\n\n// Helper function to determine content type\nfunction getContentType(pathname) {\n  const ext = pathname.split('.').pop()?.toLowerCase();\n  const types = {\n    'js': 'application/javascript',\n    'css': 'text/css',\n    'json': 'application/json',\n    'png': 'image/png',\n    'jpg': 'image/jpeg',\n    'jpeg': 'image/jpeg',\n    'svg': 'image/svg+xml',\n    'woff': 'font/woff',\n    'woff2': 'font/woff2',\n    'html': 'text/html',\n    'ico': 'image/x-icon',\n    'txt': 'text/plain'\n  };\n  return types[ext] || 'application/octet-stream';\n}\n\n\napp.onError((err, c) => {\n  console.error('Worker error:', err);\n  return c.json({ error: { code: 'INTERNAL_SERVER_ERROR', message: 'An unexpected error occurred' } }, 500);\n});\n\napp.notFound((c) => {\n  const url = new URL(c.req.url);\n  if (url.pathname.startsWith('/api/')) {\n    return c.json({ error: { code: 'NOT_FOUND', message: 'API endpoint not found' } }, 404);\n  }\n  return c.text('Page not found', { status: 404 });\n});\n\nexport default {\n  async fetch(request, env, ctx) {\n    ctx.waitUntil(\n      (async () => {\n        try {\n          await initializeDefaultGenres(env.READING_ASSISTANT_KV);\n          await initializeDefaultSettings(env.READING_ASSISTANT_KV);\n        } catch (error) {\n          console.error('Failed to initialize default data:', error);\n        }\n      })()\n    );\n\n    return app.fetch(request, { ...env, ctx });\n  }\n};", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTH<PERSON>_EXPORTS from \"/Users/<USER>/CascadeProjects/Reading manager/src/worker.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"/Users/<USER>/CascadeProjects/Reading manager/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"/Users/<USER>/CascadeProjects/Reading manager/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"/Users/<USER>/CascadeProjects/Reading manager/src/worker.js\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"/Users/<USER>/CascadeProjects/Reading manager/.wrangler/tmp/bundle-MXbvgj/middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"/Users/<USER>/CascadeProjects/Reading manager/node_modules/wrangler/templates/middleware/common.ts\";\nimport type { WorkerEntrypointConstructor } from \"/Users/<USER>/CascadeProjects/Reading manager/.wrangler/tmp/bundle-MXbvgj/middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"/Users/<USER>/CascadeProjects/Reading manager/.wrangler/tmp/bundle-MXbvgj/middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AACA,IAAI,UAAU,wBAAC,YAAY,SAAS,eAAe;AACjD,SAAO,CAAC,SAAS,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC;AACjB,mBAAe,SAAS,GAAG;AACzB,UAAI,KAAK,OAAO;AACd,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AACA,cAAQ;AACR,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,UAAI,WAAW,CAAC,GAAG;AACjB,kBAAU,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,gBAAQ,IAAI,aAAa;AAAA,MAC3B,OAAO;AACL,kBAAU,MAAM,WAAW,UAAU,QAAQ;AAAA,MAC/C;AACA,UAAI,SAAS;AACX,YAAI;AACF,gBAAM,MAAM,QAAQ,SAAS,MAAM,SAAS,IAAI,CAAC,CAAC;AAAA,QACpD,SAAS,KAAK;AACZ,cAAI,eAAe,SAAS,SAAS;AACnC,oBAAQ,QAAQ;AAChB,kBAAM,MAAM,QAAQ,KAAK,OAAO;AAChC,sBAAU;AAAA,UACZ,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,cAAc,SAAS,YAAY;AAC7C,gBAAM,MAAM,WAAW,OAAO;AAAA,QAChC;AAAA,MACF;AACA,UAAI,QAAQ,QAAQ,cAAc,SAAS,UAAU;AACnD,gBAAQ,MAAM;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAnCe;AAAA,EAoCjB;AACF,GAzCc;;;ACAd,IAAI,mBAAmB,OAAO;;;ACC9B,IAAI,YAAY,8BAAO,SAAS,UAA0B,uBAAO,OAAO,IAAI,MAAM;AAChF,QAAM,EAAE,MAAM,OAAO,MAAM,MAAM,IAAI;AACrC,QAAM,UAAU,mBAAmB,cAAc,QAAQ,IAAI,UAAU,QAAQ;AAC/E,QAAM,cAAc,QAAQ,IAAI,cAAc;AAC9C,MAAI,aAAa,WAAW,qBAAqB,KAAK,aAAa,WAAW,mCAAmC,GAAG;AAClH,WAAO,cAAc,SAAS,EAAE,KAAK,IAAI,CAAC;AAAA,EAC5C;AACA,SAAO,CAAC;AACV,GARgB;AAShB,eAAe,cAAc,SAAS,SAAS;AAC7C,QAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,MAAI,UAAU;AACZ,WAAO,0BAA0B,UAAU,OAAO;AAAA,EACpD;AACA,SAAO,CAAC;AACV;AANe;AAOf,SAAS,0BAA0B,UAAU,SAAS;AACpD,QAAM,OAAuB,uBAAO,OAAO,IAAI;AAC/C,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,uBAAuB,QAAQ,OAAO,IAAI,SAAS,IAAI;AAC7D,QAAI,CAAC,sBAAsB;AACzB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,6BAAuB,MAAM,KAAK,KAAK;AAAA,IACzC;AAAA,EACF,CAAC;AACD,MAAI,QAAQ,KAAK;AACf,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC7C,YAAM,uBAAuB,IAAI,SAAS,GAAG;AAC7C,UAAI,sBAAsB;AACxB,kCAA0B,MAAM,KAAK,KAAK;AAC1C,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AApBS;AAqBT,IAAI,yBAAyB,wBAAC,MAAM,KAAK,UAAU;AACjD,MAAI,KAAK,GAAG,MAAM,QAAQ;AACxB,QAAI,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG;AAC5B;AACA,WAAK,GAAG,EAAE,KAAK,KAAK;AAAA,IACtB,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK,GAAG,GAAG,KAAK;AAAA,IAC/B;AAAA,EACF,OAAO;AACL,QAAI,CAAC,IAAI,SAAS,IAAI,GAAG;AACvB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,WAAK,GAAG,IAAI,CAAC,KAAK;AAAA,IACpB;AAAA,EACF;AACF,GAf6B;AAgB7B,IAAI,4BAA4B,wBAAC,MAAM,KAAK,UAAU;AACpD,MAAI,aAAa;AACjB,QAAM,OAAO,IAAI,MAAM,GAAG;AAC1B,OAAK,QAAQ,CAAC,MAAM,UAAU;AAC5B,QAAI,UAAU,KAAK,SAAS,GAAG;AAC7B,iBAAW,IAAI,IAAI;AAAA,IACrB,OAAO;AACL,UAAI,CAAC,WAAW,IAAI,KAAK,OAAO,WAAW,IAAI,MAAM,YAAY,MAAM,QAAQ,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,aAAa,MAAM;AACpI,mBAAW,IAAI,IAAoB,uBAAO,OAAO,IAAI;AAAA,MACvD;AACA,mBAAa,WAAW,IAAI;AAAA,IAC9B;AAAA,EACF,CAAC;AACH,GAbgC;;;ACtDhC,IAAI,YAAY,wBAAC,SAAS;AACxB,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM,CAAC,MAAM,IAAI;AACnB,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT,GANgB;AAOhB,IAAI,mBAAmB,wBAAC,cAAc;AACpC,QAAM,EAAE,QAAQ,KAAK,IAAI,sBAAsB,SAAS;AACxD,QAAM,QAAQ,UAAU,IAAI;AAC5B,SAAO,kBAAkB,OAAO,MAAM;AACxC,GAJuB;AAKvB,IAAI,wBAAwB,wBAAC,SAAS;AACpC,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,QAAQ,cAAc,CAAC,OAAO,UAAU;AAClD,UAAM,OAAO,IAAI,KAAK;AACtB,WAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AACzB,WAAO;AAAA,EACT,CAAC;AACD,SAAO,EAAE,QAAQ,KAAK;AACxB,GAR4B;AAS5B,IAAI,oBAAoB,wBAAC,OAAO,WAAW;AACzC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAI,MAAM,CAAC,EAAE,SAAS,IAAI,GAAG;AAC3B,cAAM,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAXwB;AAYxB,IAAI,eAAe,CAAC;AACpB,IAAI,aAAa,wBAAC,OAAO,SAAS;AAChC,MAAI,UAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM,MAAM,6BAA6B;AACvD,MAAI,OAAO;AACT,UAAM,WAAW,GAAG,KAAK,IAAI,IAAI;AACjC,QAAI,CAAC,aAAa,QAAQ,GAAG;AAC3B,UAAI,MAAM,CAAC,GAAG;AACZ,qBAAa,QAAQ,IAAI,QAAQ,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,CAAC,UAAU,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAAA,MACpL,OAAO;AACL,qBAAa,QAAQ,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,MACjD;AAAA,IACF;AACA,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT,GAjBiB;AAkBjB,IAAI,YAAY,wBAAC,KAAK,YAAY;AAChC,MAAI;AACF,WAAO,QAAQ,GAAG;AAAA,EACpB,QAAQ;AACN,WAAO,IAAI,QAAQ,yBAAyB,CAAC,UAAU;AACrD,UAAI;AACF,eAAO,QAAQ,KAAK;AAAA,MACtB,QAAQ;AACN,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF,GAZgB;AAahB,IAAI,eAAe,wBAAC,QAAQ,UAAU,KAAK,SAAS,GAAjC;AACnB,IAAI,UAAU,wBAAC,YAAY;AACzB,QAAM,MAAM,QAAQ;AACpB,QAAM,QAAQ,IAAI,QAAQ,KAAK,IAAI,QAAQ,GAAG,IAAI,CAAC;AACnD,MAAI,IAAI;AACR,SAAO,IAAI,IAAI,QAAQ,KAAK;AAC1B,UAAM,WAAW,IAAI,WAAW,CAAC;AACjC,QAAI,aAAa,IAAI;AACnB,YAAM,aAAa,IAAI,QAAQ,KAAK,CAAC;AACrC,YAAM,OAAO,IAAI,MAAM,OAAO,eAAe,KAAK,SAAS,UAAU;AACrE,aAAO,aAAa,KAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,QAAQ,OAAO,IAAI,IAAI;AAAA,IACjF,WAAW,aAAa,IAAI;AAC1B;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,MAAM,OAAO,CAAC;AAC3B,GAfc;AAoBd,IAAI,kBAAkB,wBAAC,YAAY;AACjC,QAAM,SAAS,QAAQ,OAAO;AAC9B,SAAO,OAAO,SAAS,KAAK,OAAO,GAAG,EAAE,MAAM,MAAM,OAAO,MAAM,GAAG,EAAE,IAAI;AAC5E,GAHsB;AAItB,IAAI,YAAY,wBAAC,MAAM,QAAQ,SAAS;AACtC,MAAI,KAAK,QAAQ;AACf,UAAM,UAAU,KAAK,GAAG,IAAI;AAAA,EAC9B;AACA,SAAO,GAAG,OAAO,CAAC,MAAM,MAAM,KAAK,GAAG,GAAG,IAAI,GAAG,QAAQ,MAAM,KAAK,GAAG,MAAM,GAAG,EAAE,MAAM,MAAM,KAAK,GAAG,GAAG,MAAM,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,GAAG,EAAE;AACjJ,GALgB;AAMhB,IAAI,yBAAyB,wBAAC,SAAS;AACrC,MAAI,KAAK,WAAW,KAAK,SAAS,CAAC,MAAM,MAAM,CAAC,KAAK,SAAS,GAAG,GAAG;AAClE,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,GAAG;AAC/B,QAAM,UAAU,CAAC;AACjB,MAAI,WAAW;AACf,WAAS,QAAQ,CAAC,YAAY;AAC5B,QAAI,YAAY,MAAM,CAAC,KAAK,KAAK,OAAO,GAAG;AACzC,kBAAY,MAAM;AAAA,IACpB,WAAW,KAAK,KAAK,OAAO,GAAG;AAC7B,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,YAAI,QAAQ,WAAW,KAAK,aAAa,IAAI;AAC3C,kBAAQ,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,kBAAQ,KAAK,QAAQ;AAAA,QACvB;AACA,cAAM,kBAAkB,QAAQ,QAAQ,KAAK,EAAE;AAC/C,oBAAY,MAAM;AAClB,gBAAQ,KAAK,QAAQ;AAAA,MACvB,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AACvD,GA1B6B;AA2B7B,IAAI,aAAa,wBAAC,UAAU;AAC1B,MAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,YAAQ,MAAM,QAAQ,OAAO,GAAG;AAAA,EAClC;AACA,SAAO,MAAM,QAAQ,GAAG,MAAM,KAAK,UAAU,OAAO,mBAAmB,IAAI;AAC7E,GARiB;AASjB,IAAI,iBAAiB,wBAAC,KAAK,KAAK,aAAa;AAC3C,MAAI;AACJ,MAAI,CAAC,YAAY,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACzC,QAAI,YAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AACxC,QAAI,cAAc,IAAI;AACpB,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AAAA,IACtC;AACA,WAAO,cAAc,IAAI;AACvB,YAAM,kBAAkB,IAAI,WAAW,YAAY,IAAI,SAAS,CAAC;AACjE,UAAI,oBAAoB,IAAI;AAC1B,cAAM,aAAa,YAAY,IAAI,SAAS;AAC5C,cAAM,WAAW,IAAI,QAAQ,KAAK,UAAU;AAC5C,eAAO,WAAW,IAAI,MAAM,YAAY,aAAa,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC9E,WAAW,mBAAmB,MAAM,MAAM,eAAe,GAAG;AAC1D,eAAO;AAAA,MACT;AACA,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,YAAY,CAAC;AAAA,IAClD;AACA,cAAU,OAAO,KAAK,GAAG;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,CAAC;AACjB,cAAY,OAAO,KAAK,GAAG;AAC3B,MAAI,WAAW,IAAI,QAAQ,KAAK,CAAC;AACjC,SAAO,aAAa,IAAI;AACtB,UAAM,eAAe,IAAI,QAAQ,KAAK,WAAW,CAAC;AAClD,QAAI,aAAa,IAAI,QAAQ,KAAK,QAAQ;AAC1C,QAAI,aAAa,gBAAgB,iBAAiB,IAAI;AACpD,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,IAAI;AAAA,MACb,WAAW;AAAA,MACX,eAAe,KAAK,iBAAiB,KAAK,SAAS,eAAe;AAAA,IACpE;AACA,QAAI,SAAS;AACX,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW;AACX,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AACA,QAAI;AACJ,QAAI,eAAe,IAAI;AACrB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,IAAI,MAAM,aAAa,GAAG,iBAAiB,KAAK,SAAS,YAAY;AAC7E,UAAI,SAAS;AACX,gBAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,EAAE,QAAQ,IAAI,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACpD,gBAAQ,IAAI,IAAI,CAAC;AAAA,MACnB;AACA;AACA,cAAQ,IAAI,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO;AACL,cAAQ,IAAI,MAAM;AAAA,IACpB;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,GAAG,IAAI;AAC9B,GA/DqB;AAgErB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB,wBAAC,KAAK,QAAQ;AACjC,SAAO,eAAe,KAAK,KAAK,IAAI;AACtC,GAFqB;AAGrB,IAAI,sBAAsB;;;ACrM1B,IAAI,wBAAwB,wBAAC,QAAQ,UAAU,KAAK,mBAAmB,GAA3C;AAC5B,IAAI,cAAc,MAAM;AAAA,EALxB,OAKwB;AAAA;AAAA;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,CAAC;AAAA,EACb,YAAY,SAAS,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,GAAG;AACnD,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,MAAM,KAAK;AACT,WAAO,MAAM,KAAK,iBAAiB,GAAG,IAAI,KAAK,qBAAqB;AAAA,EACtE;AAAA,EACA,iBAAiB,KAAK;AACpB,UAAM,WAAW,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG;AAC7D,UAAM,QAAQ,KAAK,eAAe,QAAQ;AAC1C,WAAO,SAAS,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI;AAAA,EACpE;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO,OAAO,KAAK,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,CAAC;AACjE,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,KAAK,eAAe,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC;AAC/E,UAAI,UAAU,QAAQ;AACpB,gBAAQ,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,sBAAsB,KAAK,IAAI;AAAA,MACnE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe,UAAU;AACvB,WAAO,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,EAAE,QAAQ,IAAI;AAAA,EACjE;AAAA,EACA,MAAM,KAAK;AACT,WAAO,cAAc,KAAK,KAAK,GAAG;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,eAAe,KAAK,KAAK,GAAG;AAAA,EACrC;AAAA,EACA,OAAO,MAAM;AACX,QAAI,MAAM;AACR,aAAO,KAAK,IAAI,QAAQ,IAAI,IAAI,KAAK;AAAA,IACvC;AACA,UAAM,aAAa,CAAC;AACpB,SAAK,IAAI,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,iBAAW,GAAG,IAAI;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,MAAM,UAAU,SAAS;AACvB,WAAO,KAAK,UAAU,eAAe,MAAM,UAAU,MAAM,OAAO;AAAA,EACpE;AAAA,EACA,cAAc,wBAAC,QAAQ;AACrB,UAAM,EAAE,WAAW,KAAAA,KAAI,IAAI;AAC3B,UAAM,aAAa,UAAU,GAAG;AAChC,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,UAAM,eAAe,OAAO,KAAK,SAAS,EAAE,CAAC;AAC7C,QAAI,cAAc;AAChB,aAAO,UAAU,YAAY,EAAE,KAAK,CAAC,SAAS;AAC5C,YAAI,iBAAiB,QAAQ;AAC3B,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AACA,eAAO,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAAA,MACjC,CAAC;AAAA,IACH;AACA,WAAO,UAAU,GAAG,IAAIA,KAAI,GAAG,EAAE;AAAA,EACnC,GAhBc;AAAA,EAiBd,OAAO;AACL,WAAO,KAAK,YAAY,MAAM,EAAE,KAAK,CAAC,SAAS,KAAK,MAAM,IAAI,CAAC;AAAA,EACjE;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,YAAY,MAAM;AAAA,EAChC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,YAAY,UAAU;AAAA,EACpC;AAAA,EACA,iBAAiB,QAAQ,MAAM;AAC7B,SAAK,eAAe,MAAM,IAAI;AAAA,EAChC;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,KAAK,eAAe,MAAM;AAAA,EACnC;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,KAAK,gBAAgB,IAAI;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK;AAAA,EACxD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,EAAE,KAAK,UAAU,EAAE;AAAA,EAC3E;AACF;;;AC/GA,IAAI,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,QAAQ;AACV;AACA,IAAI,MAAM,wBAAC,OAAO,cAAc;AAC9B,QAAM,gBAAgB,IAAI,OAAO,KAAK;AACtC,gBAAc,YAAY;AAC1B,gBAAc,YAAY;AAC1B,SAAO;AACT,GALU;AAgFV,IAAI,kBAAkB,8BAAO,KAAK,OAAO,mBAAmB,SAAS,WAAW;AAC9E,MAAI,OAAO,QAAQ,YAAY,EAAE,eAAe,SAAS;AACvD,QAAI,EAAE,eAAe,UAAU;AAC7B,YAAM,IAAI,SAAS;AAAA,IACrB;AACA,QAAI,eAAe,SAAS;AAC1B,YAAM,MAAM;AAAA,IACd;AAAA,EACF;AACA,QAAM,YAAY,IAAI;AACtB,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO,QAAQ,QAAQ,GAAG;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,WAAO,CAAC,KAAK;AAAA,EACf,OAAO;AACL,aAAS,CAAC,GAAG;AAAA,EACf;AACA,QAAM,SAAS,QAAQ,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,QAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE;AAAA,IAC9E,CAAC,QAAQ,QAAQ;AAAA,MACf,IAAI,OAAO,OAAO,EAAE,IAAI,CAAC,SAAS,gBAAgB,MAAM,OAAO,OAAO,SAAS,MAAM,CAAC;AAAA,IACxF,EAAE,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EACxB;AACA,MAAI,mBAAmB;AACrB,WAAO,IAAI,MAAM,QAAQ,SAAS;AAAA,EACpC,OAAO;AACL,WAAO;AAAA,EACT;AACF,GA5BsB;;;ACnFtB,IAAI,aAAa;AACjB,IAAI,wBAAwB,wBAAC,aAAa,YAAY;AACpD,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,GAAG;AAAA,EACL;AACF,GAL4B;AAM5B,IAAI,UAAU,MAAM;AAAA,EAVpB,OAUoB;AAAA;AAAA;AAAA,EAClB;AAAA,EACA;AAAA,EACA,MAAM,CAAC;AAAA,EACP;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,KAAK,SAAS;AACxB,SAAK,cAAc;AACnB,QAAI,SAAS;AACX,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,MAAM,QAAQ;AACnB,WAAK,mBAAmB,QAAQ;AAChC,WAAK,QAAQ,QAAQ;AACrB,WAAK,eAAe,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,SAAK,SAAS,IAAI,YAAY,KAAK,aAAa,KAAK,OAAO,KAAK,YAAY;AAC7E,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,iBAAiB,iBAAiB,KAAK,eAAe;AAC7D,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,gCAAgC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,sCAAsC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,SAAS,IAAI,SAAS,MAAM;AAAA,MACtC,SAAS,KAAK,qBAAqB,IAAI,QAAQ;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,IAAI,IAAI,MAAM;AACZ,QAAI,KAAK,QAAQ,MAAM;AACrB,aAAO,IAAI,SAAS,KAAK,MAAM,IAAI;AACnC,iBAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAChD,YAAI,MAAM,gBAAgB;AACxB;AAAA,QACF;AACA,YAAI,MAAM,cAAc;AACtB,gBAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AAC/C,eAAK,QAAQ,OAAO,YAAY;AAChC,qBAAW,UAAU,SAAS;AAC5B,iBAAK,QAAQ,OAAO,cAAc,MAAM;AAAA,UAC1C;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS,2BAAI,SAAS;AACpB,SAAK,cAAc,CAAC,YAAY,KAAK,KAAK,OAAO;AACjD,WAAO,KAAK,UAAU,GAAG,IAAI;AAAA,EAC/B,GAHS;AAAA,EAIT,YAAY,wBAAC,WAAW,KAAK,UAAU,QAA3B;AAAA,EACZ,YAAY,6BAAM,KAAK,SAAX;AAAA,EACZ,cAAc,wBAAC,aAAa;AAC1B,SAAK,YAAY;AAAA,EACnB,GAFc;AAAA,EAGd,SAAS,wBAAC,MAAM,OAAO,YAAY;AACjC,QAAI,KAAK,WAAW;AAClB,WAAK,OAAO,IAAI,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,IACpD;AACA,UAAM,UAAU,KAAK,OAAO,KAAK,KAAK,UAAU,KAAK,qBAAqB,IAAI,QAAQ;AACtF,QAAI,UAAU,QAAQ;AACpB,cAAQ,OAAO,IAAI;AAAA,IACrB,WAAW,SAAS,QAAQ;AAC1B,cAAQ,OAAO,MAAM,KAAK;AAAA,IAC5B,OAAO;AACL,cAAQ,IAAI,MAAM,KAAK;AAAA,IACzB;AAAA,EACF,GAZS;AAAA,EAaT,SAAS,wBAAC,WAAW;AACnB,SAAK,UAAU;AAAA,EACjB,GAFS;AAAA,EAGT,MAAM,wBAAC,KAAK,UAAU;AACpB,SAAK,SAAyB,oBAAI,IAAI;AACtC,SAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EAC1B,GAHM;AAAA,EAIN,MAAM,wBAAC,QAAQ;AACb,WAAO,KAAK,OAAO,KAAK,KAAK,IAAI,GAAG,IAAI;AAAA,EAC1C,GAFM;AAAA,EAGN,IAAI,MAAM;AACR,QAAI,CAAC,KAAK,MAAM;AACd,aAAO,CAAC;AAAA,IACV;AACA,WAAO,OAAO,YAAY,KAAK,IAAI;AAAA,EACrC;AAAA,EACA,aAAa,MAAM,KAAK,SAAS;AAC/B,UAAM,kBAAkB,KAAK,OAAO,IAAI,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,oBAAoB,IAAI,QAAQ;AAC1G,QAAI,OAAO,QAAQ,YAAY,aAAa,KAAK;AAC/C,YAAM,aAAa,IAAI,mBAAmB,UAAU,IAAI,UAAU,IAAI,QAAQ,IAAI,OAAO;AACzF,iBAAW,CAAC,KAAK,KAAK,KAAK,YAAY;AACrC,YAAI,IAAI,YAAY,MAAM,cAAc;AACtC,0BAAgB,OAAO,KAAK,KAAK;AAAA,QACnC,OAAO;AACL,0BAAgB,IAAI,KAAK,KAAK;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS;AACX,iBAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC5C,YAAI,OAAO,MAAM,UAAU;AACzB,0BAAgB,IAAI,GAAG,CAAC;AAAA,QAC1B,OAAO;AACL,0BAAgB,OAAO,CAAC;AACxB,qBAAW,MAAM,GAAG;AAClB,4BAAgB,OAAO,GAAG,EAAE;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK,UAAU,KAAK;AACnE,WAAO,IAAI,SAAS,MAAM,EAAE,QAAQ,SAAS,gBAAgB,CAAC;AAAA,EAChE;AAAA,EACA,cAAc,2BAAI,SAAS,KAAK,aAAa,GAAG,IAAI,GAAtC;AAAA,EACd,OAAO,wBAAC,MAAM,KAAK,YAAY,KAAK,aAAa,MAAM,KAAK,OAAO,GAA5D;AAAA,EACP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,WAAO,CAAC,KAAK,oBAAoB,CAAC,KAAK,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,YAAY,IAAI,SAAS,IAAI,IAAI,KAAK;AAAA,MAChH;AAAA,MACA;AAAA,MACA,sBAAsB,YAAY,OAAO;AAAA,IAC3C;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,QAAQ,KAAK,YAAY;AAC/B,WAAO,KAAK;AAAA,MACV,KAAK,UAAU,MAAM;AAAA,MACrB;AAAA,MACA,sBAAsB,oBAAoB,OAAO;AAAA,IACnD;AAAA,EACF,GANO;AAAA,EAOP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,UAAM,MAAM,wBAAC,UAAU,KAAK,aAAa,OAAO,KAAK,sBAAsB,4BAA4B,OAAO,CAAC,GAAnG;AACZ,WAAO,OAAO,SAAS,WAAW,gBAAgB,MAAM,yBAAyB,WAAW,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,IAAI,IAAI;AAAA,EAC7H,GAHO;AAAA,EAIP,WAAW,wBAAC,UAAU,WAAW;AAC/B,UAAM,iBAAiB,OAAO,QAAQ;AACtC,SAAK;AAAA,MACH;AAAA,MACA,CAAC,eAAe,KAAK,cAAc,IAAI,iBAAiB,UAAU,cAAc;AAAA,IAClF;AACA,WAAO,KAAK,YAAY,MAAM,UAAU,GAAG;AAAA,EAC7C,GAPW;AAAA,EAQX,WAAW,6BAAM;AACf,SAAK,qBAAqB,MAAM,IAAI,SAAS;AAC7C,WAAO,KAAK,iBAAiB,IAAI;AAAA,EACnC,GAHW;AAIb;;;AChLA,IAAI,kBAAkB;AACtB,IAAI,4BAA4B;AAChC,IAAI,UAAU,CAAC,OAAO,QAAQ,OAAO,UAAU,WAAW,OAAO;AACjE,IAAI,mCAAmC;AACvC,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAL/C,OAK+C;AAAA;AAAA;AAC/C;;;ACLA,IAAI,mBAAmB;;;ACKvB,IAAI,kBAAkB,wBAAC,MAAM;AAC3B,SAAO,EAAE,KAAK,iBAAiB,GAAG;AACpC,GAFsB;AAGtB,IAAI,eAAe,wBAAC,KAAK,MAAM;AAC7B,MAAI,iBAAiB,KAAK;AACxB,UAAM,MAAM,IAAI,YAAY;AAC5B,WAAO,EAAE,YAAY,IAAI,MAAM,GAAG;AAAA,EACpC;AACA,UAAQ,MAAM,GAAG;AACjB,SAAO,EAAE,KAAK,yBAAyB,GAAG;AAC5C,GAPmB;AAQnB,IAAI,OAAO,MAAM;AAAA,EAjBjB,OAiBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,aAAa,CAAC,GAAG,SAAS,yBAAyB;AACzD,eAAW,QAAQ,CAAC,WAAW;AAC7B,WAAK,MAAM,IAAI,CAAC,UAAU,SAAS;AACjC,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,UAAU,QAAQ,KAAK,OAAO,KAAK;AAAA,QAC1C;AACA,aAAK,QAAQ,CAAC,YAAY;AACxB,eAAK,UAAU,QAAQ,KAAK,OAAO,OAAO;AAAA,QAC5C,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,KAAK,CAAC,QAAQ,SAAS,aAAa;AACvC,iBAAW,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG;AAC7B,aAAK,QAAQ;AACb,mBAAW,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG;AAC/B,mBAAS,IAAI,CAAC,YAAY;AACxB,iBAAK,UAAU,EAAE,YAAY,GAAG,KAAK,OAAO,OAAO;AAAA,UACrD,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,MAAM,CAAC,SAAS,aAAa;AAChC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ;AACb,iBAAS,QAAQ,IAAI;AAAA,MACvB;AACA,eAAS,QAAQ,CAAC,YAAY;AAC5B,aAAK,UAAU,iBAAiB,KAAK,OAAO,OAAO;AAAA,MACrD,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,EAAE,QAAQ,GAAG,qBAAqB,IAAI;AAC5C,WAAO,OAAO,MAAM,oBAAoB;AACxC,SAAK,UAAU,UAAU,OAAO,QAAQ,WAAW,UAAU;AAAA,EAC/D;AAAA,EACA,SAAS;AACP,UAAM,QAAQ,IAAI,KAAK;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,eAAe,KAAK;AAC1B,UAAM,mBAAmB,KAAK;AAC9B,UAAM,SAAS,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,MAAM,MAAMC,OAAK;AACf,UAAM,SAAS,KAAK,SAAS,IAAI;AACjC,IAAAA,MAAI,OAAO,IAAI,CAAC,MAAM;AACpB,UAAI;AACJ,UAAIA,MAAI,iBAAiB,cAAc;AACrC,kBAAU,EAAE;AAAA,MACd,OAAO;AACL,kBAAU,8BAAO,GAAG,UAAU,MAAM,QAAQ,CAAC,GAAGA,MAAI,YAAY,EAAE,GAAG,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAtF;AACV,gBAAQ,gBAAgB,IAAI,EAAE;AAAA,MAChC;AACA,aAAO,UAAU,EAAE,QAAQ,EAAE,MAAM,OAAO;AAAA,IAC5C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,UAAM,SAAS,KAAK,OAAO;AAC3B,WAAO,YAAY,UAAU,KAAK,WAAW,IAAI;AACjD,WAAO;AAAA,EACT;AAAA,EACA,UAAU,wBAAC,YAAY;AACrB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT,GAHU;AAAA,EAIV,WAAW,wBAAC,YAAY;AACtB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT,GAHW;AAAA,EAIX,MAAM,MAAM,oBAAoB,SAAS;AACvC,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS;AACX,UAAI,OAAO,YAAY,YAAY;AACjC,wBAAgB;AAAA,MAClB,OAAO;AACL,wBAAgB,QAAQ;AACxB,YAAI,QAAQ,mBAAmB,OAAO;AACpC,2BAAiB,wBAAC,YAAY,SAAb;AAAA,QACnB,OAAO;AACL,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AACA,UAAM,aAAa,gBAAgB,CAAC,MAAM;AACxC,YAAM,WAAW,cAAc,CAAC;AAChC,aAAO,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,IACvD,IAAI,CAAC,MAAM;AACT,UAAI,mBAAmB;AACvB,UAAI;AACF,2BAAmB,EAAE;AAAA,MACvB,QAAQ;AAAA,MACR;AACA,aAAO,CAAC,EAAE,KAAK,gBAAgB;AAAA,IACjC;AACA,wBAAoB,MAAM;AACxB,YAAM,aAAa,UAAU,KAAK,WAAW,IAAI;AACjD,YAAM,mBAAmB,eAAe,MAAM,IAAI,WAAW;AAC7D,aAAO,CAAC,YAAY;AAClB,cAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAC/B,YAAI,WAAW,IAAI,SAAS,MAAM,gBAAgB,KAAK;AACvD,eAAO,IAAI,QAAQ,KAAK,OAAO;AAAA,MACjC;AAAA,IACF,GAAG;AACH,UAAM,UAAU,8BAAO,GAAG,SAAS;AACjC,YAAM,MAAM,MAAM,mBAAmB,eAAe,EAAE,IAAI,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;AAChF,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,YAAM,KAAK;AAAA,IACb,GANgB;AAOhB,SAAK,UAAU,iBAAiB,UAAU,MAAM,GAAG,GAAG,OAAO;AAC7D,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ,MAAM,SAAS;AAC/B,aAAS,OAAO,YAAY;AAC5B,WAAO,UAAU,KAAK,WAAW,IAAI;AACrC,UAAM,IAAI,EAAE,UAAU,KAAK,WAAW,MAAM,QAAQ,QAAQ;AAC5D,SAAK,OAAO,IAAI,QAAQ,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,aAAa,KAAK,GAAG;AACnB,QAAI,eAAe,OAAO;AACxB,aAAO,KAAK,aAAa,KAAK,CAAC;AAAA,IACjC;AACA,UAAM;AAAA,EACR;AAAA,EACA,UAAU,SAAS,cAAc,KAAK,QAAQ;AAC5C,QAAI,WAAW,QAAQ;AACrB,cAAQ,YAAY,IAAI,SAAS,MAAM,MAAM,KAAK,UAAU,SAAS,cAAc,KAAK,KAAK,CAAC,GAAG;AAAA,IACnG;AACA,UAAM,OAAO,KAAK,QAAQ,SAAS,EAAE,IAAI,CAAC;AAC1C,UAAM,cAAc,KAAK,OAAO,MAAM,QAAQ,IAAI;AAClD,UAAM,IAAI,IAAI,QAAQ,SAAS;AAAA,MAC7B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,YAAY,CAAC,EAAE,WAAW,GAAG;AAC/B,UAAI;AACJ,UAAI;AACF,cAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY;AAC3C,YAAE,MAAM,MAAM,KAAK,iBAAiB,CAAC;AAAA,QACvC,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AACA,aAAO,eAAe,UAAU,IAAI;AAAA,QAClC,CAAC,aAAa,aAAa,EAAE,YAAY,EAAE,MAAM,KAAK,iBAAiB,CAAC;AAAA,MAC1E,EAAE,MAAM,CAAC,QAAQ,KAAK,aAAa,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,iBAAiB,CAAC;AAAA,IAC9E;AACA,UAAM,WAAW,QAAQ,YAAY,CAAC,GAAG,KAAK,cAAc,KAAK,gBAAgB;AACjF,YAAQ,YAAY;AAClB,UAAI;AACF,cAAM,UAAU,MAAM,SAAS,CAAC;AAChC,YAAI,CAAC,QAAQ,WAAW;AACtB,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAO,QAAQ;AAAA,MACjB,SAAS,KAAK;AACZ,eAAO,KAAK,aAAa,KAAK,CAAC;AAAA,MACjC;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,QAAQ,wBAAC,YAAY,SAAS;AAC5B,WAAO,KAAK,UAAU,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,MAAM;AAAA,EACjE,GAFQ;AAAA,EAGR,UAAU,wBAAC,OAAO,aAAa,KAAK,iBAAiB;AACnD,QAAI,iBAAiB,SAAS;AAC5B,aAAO,KAAK,MAAM,cAAc,IAAI,QAAQ,OAAO,WAAW,IAAI,OAAO,KAAK,YAAY;AAAA,IAC5F;AACA,YAAQ,MAAM,SAAS;AACvB,WAAO,KAAK;AAAA,MACV,IAAI;AAAA,QACF,eAAe,KAAK,KAAK,IAAI,QAAQ,mBAAmB,UAAU,KAAK,KAAK,CAAC;AAAA,QAC7E;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAbU;AAAA,EAcV,OAAO,6BAAM;AACX,qBAAiB,SAAS,CAAC,UAAU;AACnC,YAAM,YAAY,KAAK,UAAU,MAAM,SAAS,OAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,IACtF,CAAC;AAAA,EACH,GAJO;AAKT;;;ACzOA,IAAI,oBAAoB;AACxB,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,aAAa,OAAO;AACxB,IAAI,kBAAkB,IAAI,IAAI,aAAa;AAC3C,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE,WAAW,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,EAC3C;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,6BAA6B,MAAM,2BAA2B;AACtE,WAAO;AAAA,EACT,WAAW,MAAM,6BAA6B,MAAM,2BAA2B;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,MAAM,mBAAmB;AAC3B,WAAO;AAAA,EACT,WAAW,MAAM,mBAAmB;AAClC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,EAAE;AAC/D;AAlBS;AAmBT,IAAI,OAAO,MAAM;AAAA,EAzBjB,OAyBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA,YAA4B,uBAAO,OAAO,IAAI;AAAA,EAC9C,OAAO,QAAQ,OAAO,UAAU,SAAS,oBAAoB;AAC3D,QAAI,OAAO,WAAW,GAAG;AACvB,UAAI,KAAK,WAAW,QAAQ;AAC1B,cAAM;AAAA,MACR;AACA,UAAI,oBAAoB;AACtB;AAAA,MACF;AACA,WAAK,SAAS;AACd;AAAA,IACF;AACA,UAAM,CAAC,OAAO,GAAG,UAAU,IAAI;AAC/B,UAAM,UAAU,UAAU,MAAM,WAAW,WAAW,IAAI,CAAC,IAAI,IAAI,yBAAyB,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,UAAU,OAAO,CAAC,IAAI,IAAI,yBAAyB,IAAI,MAAM,MAAM,6BAA6B;AAC9N,QAAI;AACJ,QAAI,SAAS;AACX,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,YAAY,QAAQ,CAAC,KAAK;AAC9B,UAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,YAAI,cAAc,MAAM;AACtB,gBAAM;AAAA,QACR;AACA,oBAAY,UAAU,QAAQ,0BAA0B,KAAK;AAC7D,YAAI,YAAY,KAAK,SAAS,GAAG;AAC/B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO,KAAK,UAAU,SAAS;AAC/B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,MAAM,6BAA6B,MAAM;AAAA,QAClD,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,SAAS,IAAI,IAAI,KAAK;AAC5C,YAAI,SAAS,IAAI;AACf,eAAK,YAAY,QAAQ;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,CAAC,sBAAsB,SAAS,IAAI;AACtC,iBAAS,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;AAAA,MACtC;AAAA,IACF,OAAO;AACL,aAAO,KAAK,UAAU,KAAK;AAC3B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,SAAS,EAAE;AAAA,UAC9B,CAAC,MAAM,EAAE,SAAS,KAAK,MAAM,6BAA6B,MAAM;AAAA,QAClE,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,UAAU,KAAK,IAAI,IAAI,KAAK;AAAA,MAC1C;AAAA,IACF;AACA,SAAK,OAAO,YAAY,OAAO,UAAU,SAAS,kBAAkB;AAAA,EACtE;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,OAAO,KAAK,KAAK,SAAS,EAAE,KAAK,UAAU;AAC7D,UAAM,UAAU,UAAU,IAAI,CAAC,MAAM;AACnC,YAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,cAAQ,OAAO,EAAE,cAAc,WAAW,IAAI,CAAC,KAAK,EAAE,SAAS,KAAK,gBAAgB,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE,eAAe;AAAA,IAChI,CAAC;AACD,QAAI,OAAO,KAAK,WAAW,UAAU;AACnC,cAAQ,QAAQ,IAAI,KAAK,MAAM,EAAE;AAAA,IACnC;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,WAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI;AAAA,EACrC;AACF;;;ACxGA,IAAI,OAAO,MAAM;AAAA,EAFjB,OAEiB;AAAA;AAAA;AAAA,EACf,WAAW,EAAE,UAAU,EAAE;AAAA,EACzB,QAAQ,IAAI,KAAK;AAAA,EACjB,OAAO,MAAM,OAAO,oBAAoB;AACtC,UAAM,aAAa,CAAC;AACpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,OAAO;AAClB,UAAI,WAAW;AACf,aAAO,KAAK,QAAQ,cAAc,CAAC,MAAM;AACvC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB;AACA,mBAAW;AACX,eAAO;AAAA,MACT,CAAC;AACD,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,KAAK,MAAM,0BAA0B,KAAK,CAAC;AAC1D,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AAClC,iBAAO,CAAC,IAAI,OAAO,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,OAAO,YAAY,KAAK,UAAU,kBAAkB;AAC9E,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,QAAI,SAAS,KAAK,MAAM,eAAe;AACvC,QAAI,WAAW,IAAI;AACjB,aAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,IACtB;AACA,QAAI,eAAe;AACnB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,sBAAsB,CAAC;AAC7B,aAAS,OAAO,QAAQ,yBAAyB,CAAC,GAAG,cAAc,eAAe;AAChF,UAAI,iBAAiB,QAAQ;AAC3B,4BAAoB,EAAE,YAAY,IAAI,OAAO,YAAY;AACzD,eAAO;AAAA,MACT;AACA,UAAI,eAAe,QAAQ;AACzB,4BAAoB,OAAO,UAAU,CAAC,IAAI,EAAE;AAC5C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO,CAAC,IAAI,OAAO,IAAI,MAAM,EAAE,GAAG,qBAAqB,mBAAmB;AAAA,EAC5E;AACF;;;AC9CA,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC;AAChE,IAAI,sBAAsC,uBAAO,OAAO,IAAI;AAC5D,SAAS,oBAAoB,MAAM;AACjC,SAAO,oBAAoB,IAAI,MAAM,IAAI;AAAA,IACvC,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,MAC3B;AAAA,MACA,CAAC,GAAG,aAAa,WAAW,KAAK,QAAQ,KAAK;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAPS;AAQT,SAAS,2BAA2B;AAClC,wBAAsC,uBAAO,OAAO,IAAI;AAC1D;AAFS;AAGT,SAAS,mCAAmC,QAAQ;AAClD,QAAM,OAAO,IAAI,KAAK;AACtB,QAAM,cAAc,CAAC;AACrB,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,2BAA2B,OAAO;AAAA,IACtC,CAAC,UAAU,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC,GAAG,GAAG,KAAK;AAAA,EAChD,EAAE;AAAA,IACA,CAAC,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,MAAM,YAAY,IAAI,YAAY,KAAK,MAAM,SAAS,MAAM;AAAA,EACpG;AACA,QAAM,YAA4B,uBAAO,OAAO,IAAI;AACpD,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,yBAAyB,QAAQ,IAAI,KAAK,KAAK;AAC3E,UAAM,CAAC,oBAAoB,MAAM,QAAQ,IAAI,yBAAyB,CAAC;AACvE,QAAI,oBAAoB;AACtB,gBAAU,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC,CAAC,GAAG,UAAU;AAAA,IAChG,OAAO;AACL;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,mBAAa,KAAK,OAAO,MAAM,GAAG,kBAAkB;AAAA,IACtD,SAAS,GAAG;AACV,YAAM,MAAM,aAAa,IAAI,qBAAqB,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,oBAAoB;AACtB;AAAA,IACF;AACA,gBAAY,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,UAAU,MAAM;AACjD,YAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,oBAAc;AACd,aAAO,cAAc,GAAG,cAAc;AACpC,cAAM,CAAC,KAAK,KAAK,IAAI,WAAW,UAAU;AAC1C,sBAAc,GAAG,IAAI;AAAA,MACvB;AACA,aAAO,CAAC,GAAG,aAAa;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAM,CAAC,QAAQ,qBAAqB,mBAAmB,IAAI,KAAK,YAAY;AAC5E,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,aAAS,IAAI,GAAG,OAAO,YAAY,CAAC,EAAE,QAAQ,IAAI,MAAM,KAAK;AAC3D,YAAM,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;AACjC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,KAAK;AACjD,YAAI,KAAK,CAAC,CAAC,IAAI,oBAAoB,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC;AACpB,aAAW,KAAK,qBAAqB;AACnC,eAAW,CAAC,IAAI,YAAY,oBAAoB,CAAC,CAAC;AAAA,EACpD;AACA,SAAO,CAAC,QAAQ,YAAY,SAAS;AACvC;AAxDS;AAyDT,SAAS,eAAe,YAAY,MAAM;AACxC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAW,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG;AAC3E,QAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,GAAG;AACrC,aAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAVS;AAWT,IAAI,eAAe,MAAM;AAAA,EA3FzB,OA2FyB;AAAA;AAAA;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAC5E,SAAK,UAAU,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAAA,EAC1E;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,aAAa,KAAK;AACxB,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,cAAc,CAAC,QAAQ;AAC1B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,QAAI,CAAC,WAAW,MAAM,GAAG;AACvB;AACA,OAAC,YAAY,MAAM,EAAE,QAAQ,CAAC,eAAe;AAC3C,mBAAW,MAAM,IAAoB,uBAAO,OAAO,IAAI;AACvD,eAAO,KAAK,WAAW,eAAe,CAAC,EAAE,QAAQ,CAAC,MAAM;AACtD,qBAAW,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,EAAE,CAAC,CAAC;AAAA,QAC5D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,MAAM,MAAM,KAAK,CAAC,GAAG;AAC9C,QAAI,MAAM,KAAK,IAAI,GAAG;AACpB,YAAM,KAAK,oBAAoB,IAAI;AACnC,UAAI,WAAW,iBAAiB;AAC9B,eAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,qBAAW,CAAC,EAAE,IAAI,MAAM,eAAe,WAAW,CAAC,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,QACvH,CAAC;AAAA,MACH,OAAO;AACL,mBAAW,MAAM,EAAE,IAAI,MAAM,eAAe,WAAW,MAAM,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,MACjI;AACA,aAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM;AACxC,eAAG,KAAK,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC3D,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,OAAO,CAAC,CAAC,EAAE;AAAA,YACrB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC9D;AAAA,QACF;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,UAAM,QAAQ,uBAAuB,IAAI,KAAK,CAAC,IAAI;AACnD,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,QAAQ,MAAM,CAAC;AACrB,aAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,CAAC,EAAE,KAAK,MAAM;AAAA,YACnB,GAAG,eAAe,WAAW,CAAC,GAAG,KAAK,KAAK,eAAe,WAAW,eAAe,GAAG,KAAK,KAAK,CAAC;AAAA,UACpG;AACA,iBAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,aAAa,MAAM,IAAI,CAAC,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,6BAAyB;AACzB,UAAM,WAAW,KAAK,kBAAkB;AACxC,SAAK,QAAQ,CAAC,SAAS,UAAU;AAC/B,YAAM,UAAU,SAAS,OAAO,KAAK,SAAS,eAAe;AAC7D,YAAM,cAAc,QAAQ,CAAC,EAAE,KAAK;AACpC,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC;AACpC,UAAI,CAAC,OAAO;AACV,eAAO,CAAC,CAAC,GAAG,UAAU;AAAA,MACxB;AACA,YAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;AACjC,aAAO,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,KAAK;AAAA,IAClC;AACA,WAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,EAChC;AAAA,EACA,oBAAoB;AAClB,UAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,WAAO,KAAK,KAAK,OAAO,EAAE,OAAO,OAAO,KAAK,KAAK,WAAW,CAAC,EAAE,QAAQ,CAAC,WAAW;AAClF,eAAS,MAAM,MAAM,KAAK,cAAc,MAAM;AAAA,IAChD,CAAC;AACD,SAAK,cAAc,KAAK,UAAU;AAClC,WAAO;AAAA,EACT;AAAA,EACA,cAAc,QAAQ;AACpB,UAAM,SAAS,CAAC;AAChB,QAAI,cAAc,WAAW;AAC7B,KAAC,KAAK,aAAa,KAAK,OAAO,EAAE,QAAQ,CAAC,MAAM;AAC9C,YAAM,WAAW,EAAE,MAAM,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;AAC9F,UAAI,SAAS,WAAW,GAAG;AACzB,wBAAgB;AAChB,eAAO,KAAK,GAAG,QAAQ;AAAA,MACzB,WAAW,WAAW,iBAAiB;AACrC,eAAO;AAAA,UACL,GAAG,OAAO,KAAK,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,mCAAmC,MAAM;AAAA,IAClD;AAAA,EACF;AACF;;;ACxMA,IAAI,cAAc,MAAM;AAAA,EAFxB,OAEwB;AAAA;AAAA;AAAA,EACtB,OAAO;AAAA,EACP,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC;AAAA,EACX,YAAY,MAAM;AAChB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,SAAK,QAAQ,KAAK,CAAC,QAAQ,MAAM,OAAO,CAAC;AAAA,EAC3C;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,KAAK,SAAS;AACjB,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,UAAM,UAAU,KAAK;AACrB,UAAM,SAAS,KAAK;AACpB,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,IAAI,KAAK,KAAK;AACnB,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI;AACF,iBAAS,KAAK,GAAG,OAAO,OAAO,QAAQ,KAAK,MAAM,MAAM;AACtD,iBAAO,IAAI,GAAG,OAAO,EAAE,CAAC;AAAA,QAC1B;AACA,cAAM,OAAO,MAAM,QAAQ,IAAI;AAAA,MACjC,SAAS,GAAG;AACV,YAAI,aAAa,sBAAsB;AACrC;AAAA,QACF;AACA,cAAM;AAAA,MACR;AACA,WAAK,QAAQ,OAAO,MAAM,KAAK,MAAM;AACrC,WAAK,WAAW,CAAC,MAAM;AACvB,WAAK,UAAU;AACf;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,SAAK,OAAO,iBAAiB,KAAK,aAAa,IAAI;AACnD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,WAAW,KAAK,SAAS,WAAW,GAAG;AAC9C,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,WAAO,KAAK,SAAS,CAAC;AAAA,EACxB;AACF;;;ACnDA,IAAI,cAA8B,uBAAO,OAAO,IAAI;AACpD,IAAIC,QAAO,MAAM;AAAA,EAJjB,OAIiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT,UAAU;AAAA,EACV,YAAY,QAAQ,SAAS,UAAU;AACrC,SAAK,YAAY,YAA4B,uBAAO,OAAO,IAAI;AAC/D,SAAK,WAAW,CAAC;AACjB,QAAI,UAAU,SAAS;AACrB,YAAM,IAAoB,uBAAO,OAAO,IAAI;AAC5C,QAAE,MAAM,IAAI,EAAE,SAAS,cAAc,CAAC,GAAG,OAAO,EAAE;AAClD,WAAK,WAAW,CAAC,CAAC;AAAA,IACpB;AACA,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,OAAO,QAAQ,MAAM,SAAS;AAC5B,SAAK,SAAS,EAAE,KAAK;AACrB,QAAI,UAAU;AACd,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,YAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,YAAM,UAAU,WAAW,GAAG,KAAK;AACnC,YAAM,MAAM,MAAM,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAAI;AAClD,UAAI,OAAO,QAAQ,WAAW;AAC5B,kBAAU,QAAQ,UAAU,GAAG;AAC/B,YAAI,SAAS;AACX,uBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC9B;AACA;AAAA,MACF;AACA,cAAQ,UAAU,GAAG,IAAI,IAAIA,MAAK;AAClC,UAAI,SAAS;AACX,gBAAQ,UAAU,KAAK,OAAO;AAC9B,qBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC9B;AACA,gBAAU,QAAQ,UAAU,GAAG;AAAA,IACjC;AACA,YAAQ,SAAS,KAAK;AAAA,MACpB,CAAC,MAAM,GAAG;AAAA,QACR;AAAA,QACA,cAAc,aAAa,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AAAA,QACjE,OAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,QAAQ,YAAY,QAAQ;AAChD,UAAM,cAAc,CAAC;AACrB,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AACxD,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,YAAM,aAAa,EAAE,MAAM,KAAK,EAAE,eAAe;AACjD,YAAM,eAAe,CAAC;AACtB,UAAI,eAAe,QAAQ;AACzB,mBAAW,SAAyB,uBAAO,OAAO,IAAI;AACtD,oBAAY,KAAK,UAAU;AAC3B,YAAI,eAAe,eAAe,UAAU,WAAW,aAAa;AAClE,mBAAS,KAAK,GAAG,OAAO,WAAW,aAAa,QAAQ,KAAK,MAAM,MAAM;AACvE,kBAAM,MAAM,WAAW,aAAa,EAAE;AACtC,kBAAM,YAAY,aAAa,WAAW,KAAK;AAC/C,uBAAW,OAAO,GAAG,IAAI,SAAS,GAAG,KAAK,CAAC,YAAY,OAAO,GAAG,IAAI,WAAW,GAAG,KAAK,SAAS,GAAG;AACpG,yBAAa,WAAW,KAAK,IAAI;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,MAAM;AACnB,UAAM,cAAc,CAAC;AACrB,SAAK,UAAU;AACf,UAAM,UAAU;AAChB,QAAI,WAAW,CAAC,OAAO;AACvB,UAAM,QAAQ,UAAU,IAAI;AAC5B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,SAAS,MAAM,MAAM;AAC3B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,KAAK;AACrD,cAAM,OAAO,SAAS,CAAC;AACvB,cAAM,WAAW,KAAK,UAAU,IAAI;AACpC,YAAI,UAAU;AACZ,mBAAS,UAAU,KAAK;AACxB,cAAI,QAAQ;AACV,gBAAI,SAAS,UAAU,GAAG,GAAG;AAC3B,0BAAY;AAAA,gBACV,GAAG,KAAK,gBAAgB,SAAS,UAAU,GAAG,GAAG,QAAQ,KAAK,OAAO;AAAA,cACvE;AAAA,YACF;AACA,wBAAY,KAAK,GAAG,KAAK,gBAAgB,UAAU,QAAQ,KAAK,OAAO,CAAC;AAAA,UAC1E,OAAO;AACL,sBAAU,KAAK,QAAQ;AAAA,UACzB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,OAAO,KAAK,UAAU,QAAQ,IAAI,MAAM,KAAK;AAC3D,gBAAM,UAAU,KAAK,UAAU,CAAC;AAChC,gBAAM,SAAS,KAAK,YAAY,cAAc,CAAC,IAAI,EAAE,GAAG,KAAK,QAAQ;AACrE,cAAI,YAAY,KAAK;AACnB,kBAAM,UAAU,KAAK,UAAU,GAAG;AAClC,gBAAI,SAAS;AACX,0BAAY,KAAK,GAAG,KAAK,gBAAgB,SAAS,QAAQ,KAAK,OAAO,CAAC;AACvE,sBAAQ,UAAU;AAClB,wBAAU,KAAK,OAAO;AAAA,YACxB;AACA;AAAA,UACF;AACA,gBAAM,CAAC,KAAK,MAAM,OAAO,IAAI;AAC7B,cAAI,CAAC,QAAQ,EAAE,mBAAmB,SAAS;AACzC;AAAA,UACF;AACA,gBAAM,QAAQ,KAAK,UAAU,GAAG;AAChC,gBAAM,iBAAiB,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAC9C,cAAI,mBAAmB,QAAQ;AAC7B,kBAAM,IAAI,QAAQ,KAAK,cAAc;AACrC,gBAAI,GAAG;AACL,qBAAO,IAAI,IAAI,EAAE,CAAC;AAClB,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,KAAK,SAAS,MAAM,CAAC;AAC7E,kBAAI,OAAO,KAAK,MAAM,SAAS,EAAE,QAAQ;AACvC,sBAAM,UAAU;AAChB,sBAAM,iBAAiB,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,UAAU;AACnD,sBAAM,iBAAiB,cAAc,cAAc,MAAM,CAAC;AAC1D,+BAAe,KAAK,KAAK;AAAA,cAC3B;AACA;AAAA,YACF;AAAA,UACF;AACA,cAAI,YAAY,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAC1C,mBAAO,IAAI,IAAI;AACf,gBAAI,QAAQ;AACV,0BAAY,KAAK,GAAG,KAAK,gBAAgB,OAAO,QAAQ,QAAQ,KAAK,OAAO,CAAC;AAC7E,kBAAI,MAAM,UAAU,GAAG,GAAG;AACxB,4BAAY;AAAA,kBACV,GAAG,KAAK,gBAAgB,MAAM,UAAU,GAAG,GAAG,QAAQ,QAAQ,KAAK,OAAO;AAAA,gBAC5E;AAAA,cACF;AAAA,YACF,OAAO;AACL,oBAAM,UAAU;AAChB,wBAAU,KAAK,KAAK;AAAA,YACtB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW,UAAU,OAAO,cAAc,MAAM,KAAK,CAAC,CAAC;AAAA,IACzD;AACA,QAAI,YAAY,SAAS,GAAG;AAC1B,kBAAY,KAAK,CAAC,GAAG,MAAM;AACzB,eAAO,EAAE,QAAQ,EAAE;AAAA,MACrB,CAAC;AAAA,IACH;AACA,WAAO,CAAC,YAAY,IAAI,CAAC,EAAE,SAAS,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC;AAAA,EACrE;AACF;;;AC3JA,IAAI,aAAa,MAAM;AAAA,EAHvB,OAGuB;AAAA;AAAA;AAAA,EACrB,OAAO;AAAA,EACP;AAAA,EACA,cAAc;AACZ,SAAK,QAAQ,IAAIC,MAAK;AAAA,EACxB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,UAAU,uBAAuB,IAAI;AAC3C,QAAI,SAAS;AACX,eAAS,IAAI,GAAG,MAAM,QAAQ,QAAQ,IAAI,KAAK,KAAK;AAClD,aAAK,MAAM,OAAO,QAAQ,QAAQ,CAAC,GAAG,OAAO;AAAA,MAC/C;AACA;AAAA,IACF;AACA,SAAK,MAAM,OAAO,QAAQ,MAAM,OAAO;AAAA,EACzC;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,WAAO,KAAK,MAAM,OAAO,QAAQ,IAAI;AAAA,EACvC;AACF;;;ACjBA,IAAIC,QAAO,cAAc,KAAS;AAAA,EALlC,OAKkC;AAAA;AAAA;AAAA,EAChC,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,OAAO;AACb,SAAK,SAAS,QAAQ,UAAU,IAAI,YAAY;AAAA,MAC9C,SAAS,CAAC,IAAI,aAAa,GAAG,IAAI,WAAW,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;ACXA,IAAI,OAAO,wBAAC,YAAY;AACtB,QAAM,WAAW;AAAA,IACf,QAAQ;AAAA,IACR,cAAc,CAAC,OAAO,QAAQ,OAAO,QAAQ,UAAU,OAAO;AAAA,IAC9D,cAAc,CAAC;AAAA,IACf,eAAe,CAAC;AAAA,EAClB;AACA,QAAM,OAAO;AAAA,IACX,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,mBAAmB,CAAC,eAAe;AACvC,QAAI,OAAO,eAAe,UAAU;AAClC,UAAI,eAAe,KAAK;AACtB,eAAO,MAAM;AAAA,MACf,OAAO;AACL,eAAO,CAAC,WAAW,eAAe,SAAS,SAAS;AAAA,MACtD;AAAA,IACF,WAAW,OAAO,eAAe,YAAY;AAC3C,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC,WAAW,WAAW,SAAS,MAAM,IAAI,SAAS;AAAA,IAC5D;AAAA,EACF,GAAG,KAAK,MAAM;AACd,QAAM,oBAAoB,CAAC,qBAAqB;AAC9C,QAAI,OAAO,qBAAqB,YAAY;AAC1C,aAAO;AAAA,IACT,WAAW,MAAM,QAAQ,gBAAgB,GAAG;AAC1C,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF,GAAG,KAAK,YAAY;AACpB,SAAO,sCAAe,MAAM,GAAG,MAAM;AACnC,aAAS,IAAI,KAAK,OAAO;AACvB,QAAE,IAAI,QAAQ,IAAI,KAAK,KAAK;AAAA,IAC9B;AAFS;AAGT,UAAM,cAAc,MAAM,gBAAgB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACzE,QAAI,aAAa;AACf,UAAI,+BAA+B,WAAW;AAAA,IAChD;AACA,QAAI,KAAK,WAAW,KAAK;AACvB,YAAM,eAAe,EAAE,IAAI,OAAO,MAAM;AACxC,UAAI,cAAc;AAChB,YAAI,QAAQ,YAAY;AAAA,MAC1B,OAAO;AACL,YAAI,QAAQ,QAAQ;AAAA,MACtB;AAAA,IACF;AACA,QAAI,KAAK,aAAa;AACpB,UAAI,oCAAoC,MAAM;AAAA,IAChD;AACA,QAAI,KAAK,eAAe,QAAQ;AAC9B,UAAI,iCAAiC,KAAK,cAAc,KAAK,GAAG,CAAC;AAAA,IACnE;AACA,QAAI,EAAE,IAAI,WAAW,WAAW;AAC9B,UAAI,KAAK,UAAU,MAAM;AACvB,YAAI,0BAA0B,KAAK,OAAO,SAAS,CAAC;AAAA,MACtD;AACA,YAAM,eAAe,MAAM,iBAAiB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AAC3E,UAAI,aAAa,QAAQ;AACvB,YAAI,gCAAgC,aAAa,KAAK,GAAG,CAAC;AAAA,MAC5D;AACA,UAAI,UAAU,KAAK;AACnB,UAAI,CAAC,SAAS,QAAQ;AACpB,cAAM,iBAAiB,EAAE,IAAI,OAAO,gCAAgC;AACpE,YAAI,gBAAgB;AAClB,oBAAU,eAAe,MAAM,SAAS;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,SAAS,QAAQ;AACnB,YAAI,gCAAgC,QAAQ,KAAK,GAAG,CAAC;AACrD,UAAE,IAAI,QAAQ,OAAO,QAAQ,gCAAgC;AAAA,MAC/D;AACA,QAAE,IAAI,QAAQ,OAAO,gBAAgB;AACrC,QAAE,IAAI,QAAQ,OAAO,cAAc;AACnC,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AACA,UAAM,KAAK;AAAA,EACb,GAlDO;AAmDT,GApFW;;;ACAX,SAAS,kBAAkB;AACzB,QAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,QAAM,YAAY,OAAO,MAAM,YAAY,YAAY,KAAK,UAAU,YAAY,SAAS,cAAc,SAAS,MAAM;AACxH,SAAO,CAAC;AACV;AAJS;AAKT,eAAe,uBAAuB;AACpC,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,YAAY;AAClB,QAAM,YAAY,cAAc,UAAU,UAAU,cAAc,uBAAuB,OAAO,YAAY;AAC1G,QAAI;AACF,aAAO,gBAAgB,MAAM,OAAO,YAAY,OAAO,CAAC;AAAA,IAC1D,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF,GAAG,IAAI,CAAC,gBAAgB;AACxB,SAAO,CAAC;AACV;AAXe;;;ACJf,IAAI,WAAW,wBAAC,UAAU;AACxB,QAAM,CAAC,WAAW,SAAS,IAAI,CAAC,KAAK,GAAG;AACxC,QAAM,aAAa,MAAM,IAAI,CAAC,MAAM,EAAE,QAAQ,4BAA4B,OAAO,SAAS,CAAC;AAC3F,SAAO,WAAW,KAAK,SAAS;AAClC,GAJe;AAKf,IAAI,OAAO,wBAAC,UAAU;AACpB,QAAM,QAAQ,KAAK,IAAI,IAAI;AAC3B,SAAO,SAAS,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC;AAC9E,GAHW;AAIX,IAAI,cAAc,8BAAO,WAAW;AAClC,QAAM,eAAe,MAAM,qBAAqB;AAChD,MAAI,cAAc;AAChB,YAAQ,SAAS,MAAM,GAAG;AAAA,MACxB,KAAK;AACH,eAAO,WAAW,MAAM;AAAA,MAC1B,KAAK;AACH,eAAO,WAAW,MAAM;AAAA,MAC1B,KAAK;AACH,eAAO,WAAW,MAAM;AAAA,MAC1B,KAAK;AACH,eAAO,WAAW,MAAM;AAAA,IAC5B;AAAA,EACF;AACA,SAAO,GAAG,MAAM;AAClB,GAfkB;AAgBlB,eAAe,IAAI,IAAI,QAAQ,QAAQ,MAAM,SAAS,GAAG,SAAS;AAChE,QAAM,MAAM,WAAW,QAAuB,GAAG,MAAM,IAAI,MAAM,IAAI,IAAI,KAAK,GAAG,MAAM,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,YAAY,MAAM,CAAC,IAAI,OAAO;AACjJ,KAAG,GAAG;AACR;AAHe;AAIf,IAAI,SAAS,wBAAC,KAAK,QAAQ,QAAQ;AACjC,SAAO,sCAAe,QAAQ,GAAG,MAAM;AACrC,UAAM,EAAE,QAAQ,IAAI,IAAI,EAAE;AAC1B,UAAM,OAAO,IAAI,MAAM,IAAI,QAAQ,KAAK,CAAC,CAAC;AAC1C,UAAM,IAAI,IAAI,OAAsB,QAAQ,IAAI;AAChD,UAAM,QAAQ,KAAK,IAAI;AACvB,UAAM,KAAK;AACX,UAAM,IAAI,IAAI,OAAsB,QAAQ,MAAM,EAAE,IAAI,QAAQ,KAAK,KAAK,CAAC;AAAA,EAC7E,GAPO;AAQT,GATa;;;AC9Bb,IAAI,aAAa,wBAAC,YAAY;AAC5B,QAAM,cAAc,SAAS,SAAS;AACtC,SAAO,sCAAe,YAAY,GAAG,MAAM;AACzC,UAAM,SAAS,EAAE,IAAI,MAAM,WAAW,KAAK,EAAE,IAAI,MAAM,WAAW,MAAM;AACxE,UAAM,KAAK;AACX,QAAI,UAAU,EAAE,IAAI,QAAQ,IAAI,cAAc,GAAG,WAAW,kBAAkB,GAAG;AAC/E,YAAM,MAAM,MAAM,EAAE,IAAI,KAAK;AAC7B,QAAE,MAAM,IAAI,SAAS,KAAK,UAAU,KAAK,MAAM,SAAS,SAAS,CAAC,GAAG,EAAE,GAAG;AAAA,IAC5E;AAAA,EACF,GAPO;AAQT,GAViB;;;ACkFV,SAAS,aAAa;AAC3B,QAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,SAAO,gBAAgB,KAAK;AAG5B,QAAM,CAAC,IAAK,MAAM,CAAC,IAAI,KAAQ;AAC/B,QAAM,CAAC,IAAK,MAAM,CAAC,IAAI,KAAQ;AAE/B,QAAM,MAAM,MAAM,KAAK,OAAO,UAAQ,KAAK,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AACjF,SAAO,GAAG,IAAI,MAAM,GAAE,CAAC,CAAC,IAAI,IAAI,MAAM,GAAE,EAAE,CAAC,IAAI,IAAI,MAAM,IAAG,EAAE,CAAC,IAAI,IAAI,MAAM,IAAG,EAAE,CAAC,IAAI,IAAI,MAAM,IAAG,EAAE,CAAC;AACzG;AAVgB;AAgBhB,IAAM,eAAe;AAAA,EACnB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AACX;AAQA,SAAS,QAAQ,MAAM,IAAI;AACzB,QAAM,SAAS,aAAa,IAAI;AAChC,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,wBAAwB,IAAI,EAAE;AAAA,EAChD;AACA,SAAO,GAAG,MAAM,GAAG,EAAE;AACvB;AANS;AAkBT,eAAe,YAAY,IAAI,MAAM,IAAI;AACvC,QAAM,WAAW,SAAS,IAAI;AAC9B,QAAM,WAAW,MAAM,GAAG,IAAI,QAAQ;AACtC,QAAM,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI,CAAC;AAE/C,MAAI,CAAC,IAAI,SAAS,EAAE,GAAG;AACrB,QAAI,KAAK,EAAE;AACX,UAAM,GAAG,IAAI,UAAU,KAAK,UAAU,GAAG,CAAC;AAAA,EAC5C;AACF;AATe;AAiBf,eAAe,gBAAgB,IAAI,MAAM,IAAI;AAC3C,QAAM,WAAW,SAAS,IAAI;AAC9B,QAAM,WAAW,MAAM,GAAG,IAAI,QAAQ;AACtC,MAAI,CAAC,SAAU;AAEf,QAAM,MAAM,KAAK,MAAM,QAAQ,EAAE,OAAO,gBAAc,eAAe,EAAE;AACvE,QAAM,GAAG,IAAI,UAAU,KAAK,UAAU,GAAG,CAAC;AAC5C;AAPe;AAkBf,eAAsB,YAAY,IAAI;AACpC,QAAM,QAAQ,MAAM,GAAG,IAAI,gBAAgB;AAC3C,QAAM,MAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC;AAEzC,QAAM,WAAW,CAAC;AAClB,aAAW,MAAM,KAAK;AACpB,UAAM,UAAU,MAAM,GAAG,IAAI,QAAQ,YAAY,EAAE,CAAC;AACpD,QAAI,SAAS;AACX,YAAM,SAAS,KAAK,MAAM,OAAO;AAEjC,UAAI,CAAC,OAAO,iBAAiB;AAC3B,eAAO,kBAAkB,CAAC;AAAA,MAC5B;AACA,eAAS,KAAK,MAAM;AAAA,IACtB;AAAA,EACF;AACA,SAAO;AACT;AAjBsB;AAyBtB,eAAsB,eAAe,IAAI,IAAI;AAC3C,QAAM,UAAU,MAAM,GAAG,IAAI,QAAQ,YAAY,EAAE,CAAC;AACpD,SAAO,UAAU,KAAK,MAAM,OAAO,IAAI;AACzC;AAHsB;AAWtB,eAAsB,YAAY,IAAI,SAAS;AAC7C,QAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AACnC,QAAM,wBAAwB;AAAA,IAC5B,GAAG;AAAA,IACH,WAAW;AAAA,IACX,WAAW,QAAQ,aAAa;AAAA,EAClC;AAEA,QAAM,GAAG,IAAI,QAAQ,YAAY,QAAQ,EAAE,GAAG,KAAK,UAAU,qBAAqB,CAAC;AACnF,QAAM,YAAY,IAAI,YAAY,QAAQ,EAAE;AAE5C,SAAO;AACT;AAZsB;AAoBtB,eAAsB,cAAc,IAAI,IAAI;AAC1C,QAAM,GAAG,OAAO,QAAQ,YAAY,EAAE,CAAC;AACvC,QAAM,gBAAgB,IAAI,YAAY,EAAE;AACxC,SAAO;AACT;AAJsB;AAetB,eAAsB,WAAW,IAAI;AACnC,QAAM,QAAQ,MAAM,GAAG,IAAI,eAAe;AAC1C,QAAM,MAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC;AAEzC,QAAM,UAAU,CAAC;AACjB,aAAW,MAAM,KAAK;AACpB,UAAM,YAAY,MAAM,GAAG,IAAI,QAAQ,WAAW,EAAE,CAAC;AACrD,QAAI,UAAW,SAAQ,KAAK,KAAK,MAAM,SAAS,CAAC;AAAA,EACnD;AACA,SAAO;AACT;AAVsB;AAkBtB,eAAsB,aAAa,IAAI,IAAI;AACzC,QAAM,YAAY,MAAM,GAAG,IAAI,QAAQ,WAAW,EAAE,CAAC;AACrD,SAAO,YAAY,KAAK,MAAM,SAAS,IAAI;AAC7C;AAHsB;AAWtB,eAAsB,UAAU,IAAI,WAAW;AAC7C,QAAM,OAAM,oBAAI,KAAK,GAAE,YAAY;AACnC,QAAM,sBAAsB;AAAA,IAC1B,GAAG;AAAA,IACH,WAAW;AAAA,IACX,WAAW,UAAU,aAAa;AAAA,EACpC;AAEA,QAAM,GAAG,IAAI,QAAQ,WAAW,UAAU,EAAE,GAAG,KAAK,UAAU,mBAAmB,CAAC;AAClF,QAAM,YAAY,IAAI,WAAW,UAAU,EAAE;AAE7C,SAAO;AACT;AAZsB;AAoBtB,eAAsB,YAAY,IAAI,IAAI;AACxC,QAAM,GAAG,OAAO,QAAQ,WAAW,EAAE,CAAC;AACtC,QAAM,gBAAgB,IAAI,WAAW,EAAE;AACvC,SAAO;AACT;AAJsB;AAetB,eAAsB,SAAS,IAAI;AACjC,QAAM,QAAQ,MAAM,GAAG,IAAI,aAAa;AACxC,QAAM,MAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC;AAEzC,QAAM,QAAQ,CAAC;AACf,aAAW,MAAM,KAAK;AACpB,UAAM,OAAO,MAAM,GAAG,IAAI,QAAQ,SAAS,EAAE,CAAC;AAC9C,QAAI,KAAM,OAAM,KAAK,KAAK,MAAM,IAAI,CAAC;AAAA,EACvC;AACA,SAAO;AACT;AAVsB;AAkBtB,eAAsB,YAAY,IAAI,IAAI;AACxC,QAAM,OAAO,MAAM,GAAG,IAAI,QAAQ,SAAS,EAAE,CAAC;AAC9C,SAAO,OAAO,KAAK,MAAM,IAAI,IAAI;AACnC;AAHsB;AAWtB,eAAsB,SAAS,IAAI,MAAM;AACvC,QAAM,GAAG,IAAI,QAAQ,SAAS,KAAK,EAAE,GAAG,KAAK,UAAU,IAAI,CAAC;AAC5D,QAAM,YAAY,IAAI,SAAS,KAAK,EAAE;AACtC,SAAO;AACT;AAJsB;AAYtB,eAAsB,WAAW,IAAI,IAAI;AACvC,QAAM,GAAG,OAAO,QAAQ,SAAS,EAAE,CAAC;AACpC,QAAM,gBAAgB,IAAI,SAAS,EAAE;AACrC,SAAO;AACT;AAJsB;AAetB,eAAsB,UAAU,IAAI;AAClC,QAAM,QAAQ,MAAM,GAAG,IAAI,cAAc;AACzC,QAAM,MAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC;AAEzC,QAAM,SAAS,CAAC;AAChB,aAAW,MAAM,KAAK;AACpB,UAAM,QAAQ,MAAM,GAAG,IAAI,QAAQ,UAAU,EAAE,CAAC;AAChD,QAAI,MAAO,QAAO,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,EAC1C;AACA,SAAO;AACT;AAVsB;AAkBtB,eAAsB,aAAa,IAAI,IAAI;AACzC,QAAM,QAAQ,MAAM,GAAG,IAAI,QAAQ,UAAU,EAAE,CAAC;AAChD,SAAO,QAAQ,KAAK,MAAM,KAAK,IAAI;AACrC;AAHsB;AAWtB,eAAsB,UAAU,IAAI,OAAO;AACzC,QAAM,GAAG,IAAI,QAAQ,UAAU,MAAM,EAAE,GAAG,KAAK,UAAU,KAAK,CAAC;AAC/D,QAAM,YAAY,IAAI,UAAU,MAAM,EAAE;AACxC,SAAO;AACT;AAJsB;AAYtB,eAAsB,YAAY,IAAI,IAAI;AACxC,QAAM,GAAG,OAAO,QAAQ,UAAU,EAAE,CAAC;AACrC,QAAM,gBAAgB,IAAI,UAAU,EAAE;AACtC,SAAO;AACT;AAJsB;AAetB,eAAsB,YAAY,IAAI;AACpC,QAAM,QAAQ,MAAM,GAAG,IAAI,gBAAgB;AAC3C,QAAM,MAAM,QAAQ,KAAK,MAAM,KAAK,IAAI,CAAC;AAEzC,QAAM,WAAW,CAAC;AAClB,aAAW,MAAM,KAAK;AACpB,UAAM,UAAU,MAAM,GAAG,IAAI,QAAQ,YAAY,EAAE,CAAC;AACpD,QAAI,QAAS,UAAS,KAAK,KAAK,MAAM,OAAO,CAAC;AAAA,EAChD;AACA,SAAO;AACT;AAVsB;AAkBtB,eAAsB,qBAAqB,IAAI,WAAW;AACxD,QAAM,WAAW,MAAM,YAAY,EAAE;AACrC,SAAO,SAAS,OAAO,aAAW,QAAQ,cAAc,SAAS;AACnE;AAHsB;AAWtB,eAAsB,YAAY,IAAI,SAAS;AAC7C,QAAM,GAAG,IAAI,QAAQ,YAAY,QAAQ,EAAE,GAAG,KAAK,UAAU,OAAO,CAAC;AACrE,QAAM,YAAY,IAAI,YAAY,QAAQ,EAAE;AAG5C,QAAM,UAAU,MAAM,eAAe,IAAI,QAAQ,SAAS;AAC1D,MAAI,SAAS;AACX,UAAM,YAAY,IAAI,EAAE,GAAG,SAAS,cAAc,QAAQ,KAAK,CAAC;AAAA,EAClE;AAEA,SAAO;AACT;AAXsB;AAmBtB,eAAsB,cAAc,IAAI,IAAI;AAC1C,QAAM,GAAG,OAAO,QAAQ,YAAY,EAAE,CAAC;AACvC,QAAM,gBAAgB,IAAI,YAAY,EAAE;AACxC,SAAO;AACT;AAJsB;AAetB,eAAsB,YAAY,IAAI;AACpC,QAAM,WAAW,MAAM,GAAG,IAAI,UAAU;AACxC,SAAO,WAAW,KAAK,MAAM,QAAQ,IAAI;AAC3C;AAHsB;AAWtB,eAAsBC,cAAa,IAAI,UAAU;AAC/C,QAAM,GAAG,IAAI,YAAY,KAAK,UAAU,QAAQ,CAAC;AACjD,SAAO;AACT;AAHsB,OAAAA,eAAA;AAatB,eAAsB,wBAAwB,IAAI;AAChD,QAAM,iBAAiB,MAAM,UAAU,EAAE;AACzC,MAAI,eAAe,SAAS,EAAG;AAE/B,QAAM,gBAAgB;AAAA,IACpB,EAAE,IAAI,WAAW,GAAG,MAAM,aAAa,aAAa,mDAAmD,cAAc,KAAK;AAAA,IAC1H,EAAE,IAAI,WAAW,GAAG,MAAM,WAAW,aAAa,yCAAyC,cAAc,KAAK;AAAA,IAC9G,EAAE,IAAI,WAAW,GAAG,MAAM,WAAW,aAAa,qCAAqC,cAAc,KAAK;AAAA,IAC1G,EAAE,IAAI,WAAW,GAAG,MAAM,mBAAmB,aAAa,uCAAuC,cAAc,KAAK;AAAA,IACpH,EAAE,IAAI,WAAW,GAAG,MAAM,kBAAkB,aAAa,8CAA8C,cAAc,KAAK;AAAA,IAC1H,EAAE,IAAI,WAAW,GAAG,MAAM,cAAc,aAAa,iDAAiD,cAAc,KAAK;AAAA,IACzH,EAAE,IAAI,WAAW,GAAG,MAAM,UAAU,aAAa,wCAAwC,cAAc,KAAK;AAAA,IAC5G,EAAE,IAAI,WAAW,GAAG,MAAM,UAAU,aAAa,iDAAiD,cAAc,KAAK;AAAA,IACrH,EAAE,IAAI,WAAW,GAAG,MAAM,UAAU,aAAa,sCAAsC,cAAc,KAAK;AAAA,IAC1G,EAAE,IAAI,WAAW,GAAG,MAAM,SAAS,aAAa,qCAAqC,cAAc,KAAK;AAAA,EAC1G;AAEA,aAAW,SAAS,eAAe;AACjC,UAAM,UAAU,IAAI,KAAK;AAAA,EAC3B;AACF;AApBsB;AA0BtB,eAAsB,0BAA0B,IAAI;AAClD,QAAM,mBAAmB,MAAM,YAAY,EAAE;AAC7C,MAAI,iBAAkB;AAEtB,QAAM,kBAAkB;AAAA,IACtB,uBAAuB;AAAA,MACrB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,IACtB;AAAA,EACF;AAEA,QAAMA,cAAa,IAAI,eAAe;AACxC;AAZsB;;;AClgBtB,IAAM,MAAM,IAAIC,MAAK;AAGrB,IAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,WAAW,MAAM,YAAY,EAAE,IAAI,oBAAoB;AAC7D,WAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAAA,EAClC,SAAS,OAAO;AACd,YAAQ,MAAM,4BAA4B,KAAK;AAC/C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,KAAK,OAAO,MAAM;AACzB,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,SAAS,cAAc,YAAY,IAAI;AAGrD,QAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,KAAK,EAAE,WAAW,GAAG;AACjE,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,mBAAmB,MAAM,YAAY,EAAE,IAAI,oBAAoB;AACrE,UAAM,YAAY,iBAAiB,KAAK,OAAK,EAAE,KAAK,YAAY,MAAM,KAAK,YAAY,EAAE,KAAK,CAAC;AAC/F,QAAI,WAAW;AACb,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,UAAU;AAAA,MACd,IAAI,WAAW;AAAA,MACf,MAAM,KAAK,KAAK;AAAA,MAChB,SAAS,WAAW;AAAA,MACpB,cAAc,gBAAgB;AAAA,MAC9B,cAAc;AAAA,MACd,aAAa;AAAA,QACX,kBAAkB,CAAC;AAAA,QACnB,OAAO,CAAC;AAAA,QACR,UAAU,CAAC;AAAA,QACX,gBAAgB,CAAC;AAAA,QACjB,GAAG;AAAA,MACL;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,UAAM,eAAe,MAAM,YAAY,EAAE,IAAI,sBAAsB,OAAO;AAC1E,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,UAAU,MAAM,eAAe,EAAE,IAAI,sBAAsB,EAAE;AAEnE,QAAI,CAAC,SAAS;AACZ,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC;AAAA,EACjC,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AAEjC,UAAM,kBAAkB,MAAM,eAAe,EAAE,IAAI,sBAAsB,EAAE;AAC3E,QAAI,CAAC,iBAAiB;AACpB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,QAAQ,SAAS,QAAW;AAC9B,UAAI,OAAO,QAAQ,SAAS,YAAY,QAAQ,KAAK,KAAK,EAAE,WAAW,GAAG;AACxE,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAGA,YAAM,cAAc,MAAM,YAAY,EAAE,IAAI,oBAAoB;AAChE,YAAM,YAAY,YAAY;AAAA,QAAK,OACjC,EAAE,OAAO,MAAM,EAAE,KAAK,YAAY,MAAM,QAAQ,KAAK,YAAY,EAAE,KAAK;AAAA,MAC1E;AACA,UAAI,WAAW;AACb,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AACA,cAAQ,OAAO,QAAQ,KAAK,KAAK;AAAA,IACnC;AAEA,UAAM,iBAAiB;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,UAAM,eAAe,MAAM,YAAY,EAAE,IAAI,sBAAsB,cAAc;AACjF,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAE3B,UAAM,kBAAkB,MAAM,eAAe,EAAE,IAAI,sBAAsB,EAAE;AAC3E,QAAI,CAAC,iBAAiB;AACpB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,cAAc,EAAE,IAAI,sBAAsB,EAAE;AAClD,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGD,IAAI,KAAK,SAAS,OAAO,MAAM;AAC7B,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,SAAS,IAAI;AAErB,QAAI,CAAC,MAAM,QAAQ,QAAQ,GAAG;AAC5B,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,kBAAkB,CAAC;AACzB,UAAM,SAAS,CAAC;AAEhB,eAAW,eAAe,UAAU;AAClC,UAAI;AACF,cAAM,UAAU;AAAA,UACd,IAAI,WAAW;AAAA,UACf,MAAM,YAAY,MAAM,KAAK;AAAA,UAC7B,SAAS,YAAY,WAAW;AAAA,UAChC,cAAc,YAAY,gBAAgB;AAAA,UAC1C,cAAc;AAAA,UACd,aAAa;AAAA,YACX,kBAAkB,CAAC;AAAA,YACnB,OAAO,CAAC;AAAA,YACR,UAAU,CAAC;AAAA,YACX,gBAAgB,CAAC;AAAA,YACjB,GAAG,YAAY;AAAA,UACjB;AAAA,UACA,iBAAiB,CAAC;AAAA,UAClB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,QACpC;AAGA,YAAI,CAAC,QAAQ,QAAQ,QAAQ,KAAK,WAAW,GAAG;AAC9C,iBAAO,KAAK,yBAAyB,KAAK,UAAU,WAAW,CAAC,EAAE;AAClE;AAAA,QACF;AAEA,cAAM,eAAe,MAAM,YAAY,EAAE,IAAI,sBAAsB,OAAO;AAC1E,wBAAgB,KAAK,YAAY;AAAA,MACnC,SAAS,OAAO;AACd,eAAO,KAAK,0BAA0B,YAAY,IAAI,KAAK,MAAM,OAAO,EAAE;AAAA,MAC5E;AAAA,IACF;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,QACT;AAAA,MACF;AAAA,MACA,SAAS,WAAW,gBAAgB,MAAM,YAAY,OAAO,SAAS,IAAI,KAAK,OAAO,MAAM,YAAY,EAAE;AAAA,IAC5G,GAAG,gBAAgB,SAAS,IAAI,MAAM,GAAG;AAAA,EAC3C,SAAS,OAAO;AACd,YAAQ,MAAM,kCAAkC,KAAK;AACrD,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAED,IAAO,mBAAQ;;;AC/Qf,IAAMC,OAAM,IAAIC,MAAK;AAGrBD,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,UAAU,MAAM,WAAW,EAAE,IAAI,oBAAoB;AAC3D,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC;AAAA,EACjC,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,KAAK,KAAK,OAAO,MAAM;AACzB,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,aAAa,WAAW,IAAI;AAG1C,QAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,KAAK,EAAE,WAAW,GAAG;AACjE,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,kBAAkB,MAAM,WAAW,EAAE,IAAI,oBAAoB;AACnE,UAAM,YAAY,gBAAgB,KAAK,SAAO,IAAI,KAAK,YAAY,MAAM,KAAK,YAAY,EAAE,KAAK,CAAC;AAClG,QAAI,WAAW;AACb,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,YAAY;AAAA,MAChB,IAAI,WAAW;AAAA,MACf,MAAM,KAAK,KAAK;AAAA,MAChB,aAAa,eAAe;AAAA,MAC5B,YAAY,cAAc;AAAA,MAC1B,UAAU;AAAA,MACV,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,MAClC,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,UAAM,aAAa,MAAM,UAAU,EAAE,IAAI,sBAAsB,SAAS;AACxE,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,YAAY,MAAM,aAAa,EAAE,IAAI,sBAAsB,EAAE;AAEnE,QAAI,CAAC,WAAW;AACd,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AAAA,EACnC,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AAEjC,UAAM,gBAAgB,MAAM,aAAa,EAAE,IAAI,sBAAsB,EAAE;AACvE,QAAI,CAAC,eAAe;AAClB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,QAAQ,SAAS,QAAW;AAC9B,UAAI,OAAO,QAAQ,SAAS,YAAY,QAAQ,KAAK,KAAK,EAAE,WAAW,GAAG;AACxE,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAGA,YAAM,aAAa,MAAM,WAAW,EAAE,IAAI,oBAAoB;AAC9D,YAAM,YAAY,WAAW;AAAA,QAAK,SAChC,IAAI,OAAO,MAAM,IAAI,KAAK,YAAY,MAAM,QAAQ,KAAK,YAAY,EAAE,KAAK;AAAA,MAC9E;AACA,UAAI,WAAW;AACb,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AACA,cAAQ,OAAO,QAAQ,KAAK,KAAK;AAAA,IACnC;AAEA,UAAM,eAAe;AAAA,MACnB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IACpC;AAEA,UAAM,aAAa,MAAM,UAAU,EAAE,IAAI,sBAAsB,YAAY;AAC3E,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAE3B,UAAM,gBAAgB,MAAM,aAAa,EAAE,IAAI,sBAAsB,EAAE;AACvE,QAAI,CAAC,eAAe;AAClB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,YAAY,EAAE,IAAI,sBAAsB,EAAE;AAChD,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAED,IAAO,kBAAQA;;;ACjMf,IAAME,OAAM,IAAIC,MAAK;AAGrBD,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,QAAQ,MAAM,SAAS,EAAE,IAAI,oBAAoB;AACvD,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,CAAC;AAAA,EAC/B,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,KAAK,KAAK,OAAO,MAAM;AACzB,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,OAAO,QAAQ,UAAU,cAAc,SAAS,IAAI;AAG5D,QAAI,CAAC,SAAS,OAAO,UAAU,YAAY,MAAM,KAAK,EAAE,WAAW,GAAG;AACpE,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,WAAW,UAAa,WAAW,MAAM;AAC3C,UAAI,OAAO,WAAW,YAAY,OAAO,KAAK,EAAE,WAAW,GAAG;AAC5D,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAGA,UAAM,gBAAgB,MAAM,SAAS,EAAE,IAAI,oBAAoB;AAC/D,UAAM,kBAAkB,MAAM,YAAY,EAAE,KAAK;AACjD,UAAM,mBAAmB,SAAS,OAAO,YAAY,EAAE,KAAK,IAAI;AAEhE,UAAM,YAAY,cAAc,KAAK,OAAK;AACxC,YAAM,YAAY,EAAE,MAAM,YAAY;AACtC,YAAM,aAAa,EAAE,SAAS,EAAE,OAAO,YAAY,IAAI;AAGvD,UAAI,oBAAoB,YAAY;AAClC,eAAO,cAAc,mBAAmB,eAAe;AAAA,MACzD;AAEA,UAAI,CAAC,oBAAoB,CAAC,YAAY;AACpC,eAAO,cAAc;AAAA,MACvB;AAEA,aAAO;AAAA,IACT,CAAC;AAED,QAAI,WAAW;AACb,YAAM,mBAAmB,SACrB,qDACA;AACJ,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,YAAY,MAAM,QAAQ,QAAQ,GAAG;AACvC,YAAM,SAAS,MAAM,UAAU,EAAE,IAAI,oBAAoB;AACzD,YAAM,gBAAgB,OAAO,IAAI,OAAK,EAAE,EAAE;AAC1C,YAAM,gBAAgB,SAAS,OAAO,QAAM,CAAC,cAAc,SAAS,EAAE,CAAC;AAEvE,UAAI,cAAc,SAAS,GAAG;AAC5B,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS,sBAAsB,cAAc,KAAK,IAAI,CAAC;AAAA,UACzD;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAEA,UAAM,OAAO;AAAA,MACX,IAAI,WAAW;AAAA,MACf,OAAO,MAAM,KAAK;AAAA,MAClB,QAAQ,SAAS,OAAO,KAAK,IAAI;AAAA,MACjC,UAAU,YAAY,CAAC;AAAA,MACvB,cAAc,gBAAgB;AAAA,MAC9B,UAAU,YAAY;AAAA,IACxB;AAEA,UAAM,YAAY,MAAM,SAAS,EAAE,IAAI,sBAAsB,IAAI;AACjE,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAO;AACd,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,OAAO,MAAM,YAAY,EAAE,IAAI,sBAAsB,EAAE;AAE7D,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC;AAAA,EAC9B,SAAS,OAAO;AACd,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AAEjC,UAAM,eAAe,MAAM,YAAY,EAAE,IAAI,sBAAsB,EAAE;AACrE,QAAI,CAAC,cAAc;AACjB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,QAAQ,UAAU,QAAW;AAC/B,UAAI,OAAO,QAAQ,UAAU,YAAY,QAAQ,MAAM,KAAK,EAAE,WAAW,GAAG;AAC1E,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AACA,cAAQ,QAAQ,QAAQ,MAAM,KAAK;AAAA,IACrC;AAGA,QAAI,QAAQ,WAAW,QAAW;AAChC,UAAI,OAAO,QAAQ,WAAW,YAAY,QAAQ,OAAO,KAAK,EAAE,WAAW,GAAG;AAC5E,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AACA,cAAQ,SAAS,QAAQ,OAAO,KAAK;AAAA,IACvC;AAGA,QAAI,QAAQ,UAAU,UAAa,QAAQ,WAAW,QAAW;AAC/D,YAAM,WAAW,MAAM,SAAS,EAAE,IAAI,oBAAoB;AAC1D,YAAM,YAAY,SAAS;AAAA,QAAK,OAC9B,EAAE,OAAO,MACT,EAAE,MAAM,YAAY,MAAM,QAAQ,MAAM,YAAY,KACpD,EAAE,OAAO,YAAY,MAAM,QAAQ,OAAO,YAAY;AAAA,MACxD;AACA,UAAI,WAAW;AACb,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAGA,QAAI,QAAQ,aAAa,QAAW;AAClC,UAAI,CAAC,MAAM,QAAQ,QAAQ,QAAQ,GAAG;AACpC,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAEA,YAAM,SAAS,MAAM,UAAU,EAAE,IAAI,oBAAoB;AACzD,YAAM,gBAAgB,OAAO,IAAI,OAAK,EAAE,EAAE;AAC1C,YAAM,gBAAgB,QAAQ,SAAS,OAAO,CAAAE,QAAM,CAAC,cAAc,SAASA,GAAE,CAAC;AAE/E,UAAI,cAAc,SAAS,GAAG;AAC5B,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS,sBAAsB,cAAc,KAAK,IAAI,CAAC;AAAA,UACzD;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAEA,UAAM,cAAc;AAAA,MAClB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,UAAM,YAAY,MAAM,SAAS,EAAE,IAAI,sBAAsB,WAAW;AACxE,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDF,KAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAE3B,UAAM,eAAe,MAAM,YAAY,EAAE,IAAI,sBAAsB,EAAE;AACrE,QAAI,CAAC,cAAc;AACjB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,WAAW,EAAE,IAAI,sBAAsB,EAAE;AAC/C,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,oBAAoB,OAAO,MAAM;AACvC,MAAI;AACF,UAAM,EAAE,GAAG,OAAO,QAAQ,GAAG,IAAI,EAAE,IAAI,MAAM;AAE7C,QAAI,CAAC,SAAS,MAAM,KAAK,EAAE,WAAW,GAAG;AACvC,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,YAAY,yCAAyC,mBAAmB,MAAM,KAAK,CAAC,CAAC,UAAU,KAAK;AAC1G,UAAM,WAAW,MAAM,MAAM,SAAS;AAEtC,QAAI,CAAC,SAAS,IAAI;AAChB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,OAAO,MAAM,SAAS,KAAK;AAGjC,UAAM,QAAQ,KAAK,KAAK,IAAI,UAAQ;AAAA,MAClC,IAAI;AAAA;AAAA,MACJ,OAAO,IAAI;AAAA,MACX,QAAQ,IAAI,cAAc,IAAI,YAAY,KAAK,IAAI,IAAI;AAAA,MACvD,YAAY,IAAI;AAAA,MAChB,kBAAkB,IAAI;AAAA,MACtB,YAAY,IAAI,UAAU,uCAAuC,IAAI,OAAO,WAAW;AAAA,MACvF,UAAU,IAAI,UAAU,IAAI,QAAQ,MAAM,GAAG,CAAC,IAAI,CAAC;AAAA;AAAA,MACnD,cAAc,IAAI,WAAW,IAAI,SAAS,SAAS,IAAI;AAAA,IACzD,EAAE;AAEF,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,OAAO,KAAK;AAAA,MACZ,SAAS,SAAS,MAAM,MAAM,oBAAoB,KAAK;AAAA,IACzD,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,gCAAgC,KAAK;AACnD,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,qBAAqB,OAAO,MAAM;AACxC,MAAI;AACF,UAAM,EAAE,OAAO,IAAI,EAAE,IAAI,MAAM;AAG/B,UAAM,cAAc,OAAO,QAAQ,WAAW,EAAE;AAGhD,UAAM,UAAU,iCAAiC,WAAW;AAC5D,UAAM,eAAe,MAAM,MAAM,OAAO;AAExC,QAAI,CAAC,aAAa,IAAI;AACpB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,WAAW,MAAM,aAAa,KAAK;AAGzC,QAAI,aAAa;AACjB,QAAI,SAAS,WAAW,SAAS,QAAQ,SAAS,GAAG;AACnD,YAAM,YAAY,SAAS,QAAQ,CAAC,EAAE,QAAQ,OAAO,SAAS,QAAQ,CAAC,EAAE;AACzE,UAAI,WAAW;AACb,cAAM,iBAAiB,UAAU,QAAQ,aAAa,EAAE;AACxD,cAAM,YAAY,mCAAmC,cAAc;AACnE,cAAM,iBAAiB,MAAM,MAAM,SAAS;AAC5C,YAAI,eAAe,IAAI;AACrB,uBAAa,MAAM,eAAe,KAAK;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAGA,UAAM,WAAW;AAAA,MACf,OAAO,SAAS;AAAA,MAChB,QAAQ,YAAY,QAAQ;AAAA,MAC5B,aAAa,SAAS,aAAa,SAAS,SAAS,eAAe;AAAA,MACpE,UAAU,SAAS,WAAW,SAAS,SAAS,MAAM,GAAG,EAAE,IAAI,CAAC;AAAA,MAChE,kBAAkB,SAAS,qBAAqB,IAAI,KAAK,SAAS,kBAAkB,EAAE,YAAY,IAAI;AAAA,MACtG,YAAY,SAAS,UAAU,SAAS,OAAO,SAAS,IAAI,uCAAuC,SAAS,OAAO,CAAC,CAAC,WAAW;AAAA,MAChI,YAAY,SAAS;AAAA,MACrB,WAAW,YAAY,KAAK,SAAS,YAAY,OAAO;AAAA,MACxD,iBAAiB,YAAY,cAAc;AAAA,MAC3C,iBAAiB,YAAY,cAAc;AAAA,MAC3C,WAAW,SAAS,0BAA0B;AAAA,IAChD;AAEA,WAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAAA,EAClC,SAAS,OAAO;AACd,YAAQ,MAAM,yCAAyC,KAAK;AAC5D,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,KAAK,WAAW,OAAO,MAAM;AAC/B,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,YAAY,UAAU,cAAc,SAAS,IAAI;AAEzD,QAAI,CAAC,YAAY;AACf,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,cAAc,WAAW,QAAQ,WAAW,EAAE;AACpD,UAAM,UAAU,iCAAiC,WAAW;AAC5D,UAAM,eAAe,MAAM,MAAM,OAAO;AAExC,QAAI,CAAC,aAAa,IAAI;AACpB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,WAAW,MAAM,aAAa,KAAK;AAGzC,QAAI,aAAa;AACjB,QAAI,SAAS,WAAW,SAAS,QAAQ,SAAS,GAAG;AACnD,YAAM,YAAY,SAAS,QAAQ,CAAC,EAAE,QAAQ,OAAO,SAAS,QAAQ,CAAC,EAAE;AACzE,UAAI,WAAW;AACb,cAAM,iBAAiB,UAAU,QAAQ,aAAa,EAAE;AACxD,cAAM,YAAY,mCAAmC,cAAc;AACnE,cAAM,iBAAiB,MAAM,MAAM,SAAS;AAC5C,YAAI,eAAe,IAAI;AACrB,uBAAa,MAAM,eAAe,KAAK;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAGA,UAAM,gBAAgB,MAAM,SAAS,EAAE,IAAI,oBAAoB;AAC/D,UAAM,YAAY,cAAc;AAAA,MAAK,OACnC,EAAE,MAAM,YAAY,MAAM,SAAS,MAAM,YAAY,MACpD,YAAY,QAAQ,kBAAkB,YAAY,MAAM,EAAE,OAAO,YAAY;AAAA,IAChF;AAEA,QAAI,WAAW;AACb,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,YAAY,MAAM,QAAQ,QAAQ,GAAG;AACvC,YAAM,SAAS,MAAM,UAAU,EAAE,IAAI,oBAAoB;AACzD,YAAM,gBAAgB,OAAO,IAAI,OAAK,EAAE,EAAE;AAC1C,YAAM,gBAAgB,SAAS,OAAO,QAAM,CAAC,cAAc,SAAS,EAAE,CAAC;AAEvE,UAAI,cAAc,SAAS,GAAG;AAC5B,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS,sBAAsB,cAAc,KAAK,IAAI,CAAC;AAAA,UACzD;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAGA,UAAM,OAAO;AAAA,MACX,IAAI,WAAW;AAAA,MACf,OAAO,SAAS;AAAA,MAChB,QAAQ,YAAY,QAAQ;AAAA,MAC5B,UAAU,YAAY,CAAC;AAAA,MACvB,cAAc,gBAAgB;AAAA,MAC9B,UAAU,YAAY;AAAA,MACtB,aAAa,SAAS,aAAa,SAAS,SAAS,eAAe;AAAA,MACpE,YAAY,SAAS,UAAU,SAAS,OAAO,SAAS,IAAI,uCAAuC,SAAS,OAAO,CAAC,CAAC,WAAW;AAAA,MAChI,kBAAkB,SAAS,qBAAqB,IAAI,KAAK,SAAS,kBAAkB,EAAE,YAAY,IAAI;AAAA,MACtG,YAAY,SAAS;AAAA,MACrB,cAAc;AAAA,MACd,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,IACrC;AAEA,UAAM,YAAY,MAAM,SAAS,EAAE,IAAI,sBAAsB,IAAI;AACjE,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAO;AACd,YAAQ,MAAM,0CAA0C,KAAK;AAC7D,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,KAAK,SAAS,OAAO,MAAM;AAC7B,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,IAAI;AAElB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,eAAe,CAAC;AACtB,UAAM,SAAS,CAAC;AAGhB,UAAM,SAAS,MAAM,UAAU,EAAE,IAAI,oBAAoB;AACzD,UAAM,gBAAgB,OAAO,IAAI,OAAK,EAAE,EAAE;AAE1C,eAAW,YAAY,OAAO;AAC5B,UAAI;AAEF,YAAI,WAAW,SAAS,YAAY,CAAC;AACrC,YAAI,SAAS,SAAS,GAAG;AACvB,gBAAM,gBAAgB,SAAS,OAAO,QAAM,CAAC,cAAc,SAAS,EAAE,CAAC;AACvE,cAAI,cAAc,SAAS,GAAG;AAC5B,mBAAO,KAAK,SAAS,SAAS,KAAK,4BAA4B,cAAc,KAAK,IAAI,CAAC,EAAE;AACzF;AAAA,UACF;AAAA,QACF;AAEA,cAAM,OAAO;AAAA,UACX,IAAI,WAAW;AAAA,UACf,OAAO,SAAS,OAAO,KAAK;AAAA,UAC5B,QAAQ,SAAS,QAAQ,KAAK;AAAA,UAC9B;AAAA,UACA,cAAc,SAAS,gBAAgB;AAAA,UACvC,UAAU,SAAS,YAAY;AAAA,QACjC;AAGA,YAAI,CAAC,KAAK,SAAS,KAAK,MAAM,WAAW,GAAG;AAC1C,iBAAO,KAAK,uBAAuB,KAAK,UAAU,QAAQ,CAAC,EAAE;AAC7D;AAAA,QACF;AAEA,YAAI,CAAC,KAAK,UAAU,KAAK,OAAO,WAAW,GAAG;AAC5C,iBAAO,KAAK,SAAS,KAAK,KAAK,kBAAkB;AACjD;AAAA,QACF;AAEA,cAAM,YAAY,MAAM,SAAS,EAAE,IAAI,sBAAsB,IAAI;AACjE,qBAAa,KAAK,SAAS;AAAA,MAC7B,SAAS,OAAO;AACd,eAAO,KAAK,wBAAwB,SAAS,KAAK,MAAM,MAAM,OAAO,EAAE;AAAA,MACzE;AAAA,IACF;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,QACJ,SAAS;AAAA,QACT;AAAA,MACF;AAAA,MACA,SAAS,WAAW,aAAa,MAAM,SAAS,OAAO,SAAS,IAAI,KAAK,OAAO,MAAM,YAAY,EAAE;AAAA,IACtG,GAAG,aAAa,SAAS,IAAI,MAAM,GAAG;AAAA,EACxC,SAAS,OAAO;AACd,YAAQ,MAAM,+BAA+B,KAAK;AAClD,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAED,IAAO,gBAAQA;;;ACjlBf,IAAMG,OAAM,IAAIC,MAAK;AAGrBD,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,SAAS,MAAM,UAAU,EAAE,IAAI,oBAAoB;AACzD,WAAO,EAAE,KAAK,EAAE,MAAM,OAAO,CAAC;AAAA,EAChC,SAAS,OAAO;AACd,YAAQ,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,KAAK,KAAK,OAAO,MAAM;AACzB,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,aAAa,aAAa,IAAI;AAG5C,QAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,KAAK,KAAK,EAAE,WAAW,GAAG;AACjE,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,iBAAiB,MAAM,UAAU,EAAE,IAAI,oBAAoB;AACjE,UAAM,YAAY,eAAe,KAAK,OAAK,EAAE,KAAK,YAAY,MAAM,KAAK,YAAY,EAAE,KAAK,CAAC;AAC7F,QAAI,WAAW;AACb,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,QAAQ;AAAA,MACZ,IAAI,WAAW;AAAA,MACf,MAAM,KAAK,KAAK;AAAA,MAChB,aAAa,eAAe;AAAA,MAC5B,cAAc,gBAAgB;AAAA,IAChC;AAEA,UAAM,aAAa,MAAM,UAAU,EAAE,IAAI,sBAAsB,KAAK;AACpE,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,QAAQ,MAAM,aAAa,EAAE,IAAI,sBAAsB,EAAE;AAE/D,QAAI,CAAC,OAAO;AACV,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,CAAC;AAAA,EAC/B,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AAEjC,UAAM,gBAAgB,MAAM,aAAa,EAAE,IAAI,sBAAsB,EAAE;AACvE,QAAI,CAAC,eAAe;AAClB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,cAAc,cAAc;AAC9B,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,QAAQ,SAAS,QAAW;AAC9B,UAAI,OAAO,QAAQ,SAAS,YAAY,QAAQ,KAAK,KAAK,EAAE,WAAW,GAAG;AACxE,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAGA,YAAM,YAAY,MAAM,UAAU,EAAE,IAAI,oBAAoB;AAC5D,YAAM,YAAY,UAAU;AAAA,QAAK,OAC/B,EAAE,OAAO,MAAM,EAAE,KAAK,YAAY,MAAM,QAAQ,KAAK,YAAY,EAAE,KAAK;AAAA,MAC1E;AACA,UAAI,WAAW;AACb,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AACA,cAAQ,OAAO,QAAQ,KAAK,KAAK;AAAA,IACnC;AAEA,UAAM,eAAe;AAAA,MACnB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,UAAM,aAAa,MAAM,UAAU,EAAE,IAAI,sBAAsB,YAAY;AAC3E,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAE3B,UAAM,gBAAgB,MAAM,aAAa,EAAE,IAAI,sBAAsB,EAAE;AACvE,QAAI,CAAC,eAAe;AAClB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,cAAc,cAAc;AAC9B,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,YAAY,EAAE,IAAI,sBAAsB,EAAE;AAChD,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAED,IAAO,iBAAQA;;;AChNf,IAAME,OAAM,IAAIC,MAAK;AAGrBD,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,WAAW,MAAM,YAAY,EAAE,IAAI,oBAAoB;AAC7D,WAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAAA,EAClC,SAAS,OAAO;AACd,YAAQ,MAAM,4BAA4B,KAAK;AAC/C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,KAAK,KAAK,OAAO,MAAM;AACzB,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAGJ,QAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,QAAI,CAAC,cAAc,OAAO,eAAe,YAAY,WAAW,KAAK,EAAE,WAAW,GAAG;AACnF,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,QAAI,CAAC,eAAe,CAAC,CAAC,UAAU,MAAM,EAAE,SAAS,WAAW,GAAG;AAC7D,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,kBAAkB,CAAC,CAAC,SAAS,OAAO,UAAU,EAAE,SAAS,cAAc,GAAG;AAC5E,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,QAAI,CAAC,aAAa,OAAO,cAAc,UAAU;AAC/C,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,UAAU,MAAM,eAAe,EAAE,IAAI,sBAAsB,SAAS;AAC1E,QAAI,CAAC,SAAS;AACZ,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,QAAQ;AACV,YAAM,OAAO,MAAM,YAAY,EAAE,IAAI,sBAAsB,MAAM;AACjE,UAAI,CAAC,MAAM;AACT,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAIA,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAElB,QAAI,UAAU,CAAC,WAAW;AACxB,YAAM,OAAO,MAAM,YAAY,EAAE,IAAI,sBAAsB,MAAM;AACjE,UAAI,MAAM;AACR,yBAAiB,KAAK;AACtB,sBAAc,KAAK;AAAA,MACrB;AAAA,IACF;AAEA,QAAI,CAAC,kBAAkB,CAAC,aAAa;AACnC,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,UAAU;AAAA,MACd,IAAI,WAAW;AAAA,MACf;AAAA,MACA,QAAQ,UAAU;AAAA,MAClB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,YAAY,WAAW,KAAK;AAAA,MAC5B,OAAO,QAAQ,MAAM,KAAK,IAAI;AAAA,MAC9B;AAAA,MACA;AAAA,MACA,gBAAgB,kBAAkB;AAAA,IACpC;AAEA,UAAM,eAAe,MAAM,YAAY,EAAE,IAAI,sBAAsB,OAAO;AAC1E,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,GAAG,GAAG;AAAA,EACR,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,WAAW,MAAM,YAAY,EAAE,IAAI,oBAAoB;AAC7D,UAAM,UAAU,SAAS,KAAK,OAAK,EAAE,OAAO,EAAE;AAE9C,QAAI,CAAC,SAAS;AACZ,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC;AAAA,EACjC,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,QAAQ,OAAO,MAAM;AAC3B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAC3B,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AAEjC,UAAM,WAAW,MAAM,YAAY,EAAE,IAAI,oBAAoB;AAC7D,UAAM,kBAAkB,SAAS,KAAK,OAAK,EAAE,OAAO,EAAE;AAEtD,QAAI,CAAC,iBAAiB;AACpB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,QAAQ,eAAe,CAAC,CAAC,UAAU,MAAM,EAAE,SAAS,QAAQ,WAAW,GAAG;AAC5E,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,QAAI,QAAQ,WAAW;AACrB,YAAM,UAAU,MAAM,eAAe,EAAE,IAAI,sBAAsB,QAAQ,SAAS;AAClF,UAAI,CAAC,SAAS;AACZ,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAGA,QAAI,QAAQ,QAAQ;AAClB,YAAM,OAAO,MAAM,YAAY,EAAE,IAAI,sBAAsB,QAAQ,MAAM;AACzE,UAAI,CAAC,MAAM;AACT,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAGA,UAAI,MAAM;AACR,gBAAQ,YAAY,KAAK;AACzB,gBAAQ,SAAS,KAAK;AAAA,MACxB;AAAA,IACF;AAEA,UAAM,iBAAiB;AAAA,MACrB,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAGA,UAAM,cAAc,EAAE,IAAI,sBAAsB,EAAE;AAClD,UAAM,eAAe,MAAM,YAAY,EAAE,IAAI,sBAAsB,cAAc;AAEjF,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,OAAO,QAAQ,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAE3B,UAAM,WAAW,MAAM,YAAY,EAAE,IAAI,oBAAoB;AAC7D,UAAM,kBAAkB,SAAS,KAAK,OAAK,EAAE,OAAO,EAAE;AAEtD,QAAI,CAAC,iBAAiB;AACpB,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,cAAc,EAAE,IAAI,sBAAsB,EAAE;AAClD,WAAO,EAAE,KAAK;AAAA,MACZ,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,gBAAgB,OAAO,MAAM;AACnC,MAAI;AACF,UAAM,EAAE,GAAG,IAAI,EAAE,IAAI,MAAM;AAG3B,UAAM,UAAU,MAAM,eAAe,EAAE,IAAI,sBAAsB,EAAE;AACnE,QAAI,CAAC,SAAS;AACZ,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,WAAW,MAAM,qBAAqB,EAAE,IAAI,sBAAsB,EAAE;AAC1E,WAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAAA,EAClC,SAAS,OAAO;AACd,YAAQ,MAAM,oCAAoC,KAAK;AACvD,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAED,IAAO,mBAAQA;;;AC5Uf,IAAME,OAAM,IAAIC,MAAK;AAGrBD,KAAI,KAAK,KAAK,OAAO,MAAM;AACzB,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,WAAW,QAAQ,EAAE,IAAI;AAGjC,QAAI,CAAC,aAAa,OAAO,cAAc,UAAU;AAC/C,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,UAAU,MAAM,eAAe,EAAE,IAAI,sBAAsB,SAAS;AAC1E,QAAI,CAAC,SAAS;AACZ,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAGA,UAAM,WAAW,MAAM,qBAAqB,EAAE,IAAI,sBAAsB,SAAS;AACjF,UAAM,WAAW,MAAM,SAAS,EAAE,IAAI,oBAAoB;AAC1D,UAAM,SAAS,MAAM,UAAU,EAAE,IAAI,oBAAoB;AAGzD,UAAM,cAAc,IAAI,IAAI,SAAS,IAAI,aAAW,QAAQ,MAAM,EAAE,OAAO,OAAO,CAAC;AAInF,UAAM,kBAAkB,MAAM;AAAA,MAC5B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,qCAAqC,KAAK;AACxD,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAMD,eAAe,wBAAwB,SAAS,UAAU,UAAU,QAAQ,aAAa,OAAO;AAC9F,QAAM,kBAAkB,CAAC;AACzB,QAAM,qBAAqB,QAAQ,eAAe,CAAC;AAGnD,QAAM,cAAc,SAAS,OAAO,UAAQ,CAAC,YAAY,IAAI,KAAK,EAAE,CAAC;AAGrE,QAAM,cAAc,YAAY,IAAI,UAAQ;AAC1C,QAAI,QAAQ;AAGZ,QAAI,mBAAmB,oBAAoB,mBAAmB,iBAAiB,SAAS,GAAG;AACzF,YAAM,iBAAiB,KAAK,SAAS;AAAA,QAAO,aAC1C,mBAAmB,iBAAiB,SAAS,OAAO;AAAA,MACtD;AACA,eAAS,eAAe,SAAS;AAAA,IACnC;AAGA,QAAI,mBAAmB,YAAY,mBAAmB,SAAS,SAAS,GAAG;AACzE,YAAM,iBAAiB,OAAO;AAAA,QAAO,WACnC,mBAAmB,SAAS;AAAA,UAAK,aAC/B,MAAM,KAAK,YAAY,EAAE,SAAS,QAAQ,YAAY,CAAC;AAAA,QACzD;AAAA,MACF;AACA,YAAM,mBAAmB,eAAe,IAAI,OAAK,EAAE,EAAE;AACrD,YAAM,mBAAmB,KAAK,SAAS,KAAK,aAAW,iBAAiB,SAAS,OAAO,CAAC;AACzF,UAAI,kBAAkB;AACpB,iBAAS;AAAA,MACX;AAAA,IACF;AAGA,QAAI,QAAQ,gBAAgB,KAAK,cAAc;AAC7C,UAAI,QAAQ,iBAAiB,KAAK,cAAc;AAC9C,iBAAS;AAAA,MACX;AAAA,IACF;AAGA,QAAI,KAAK,UAAU;AAGjB,eAAS;AAAA,IACX;AAIA,aAAS,KAAK,OAAO,IAAI;AAEzB,WAAO,EAAE,MAAM,MAAM;AAAA,EACvB,CAAC;AAGD,cAAY,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAE5C,SAAO,YAAY,MAAM,GAAG,KAAK,EAAE,IAAI,WAAS;AAAA,IAC9C,MAAM,KAAK;AAAA,IACX,OAAO,KAAK;AAAA,IACZ,WAAW,kBAAkB,SAAS,KAAK,MAAM,QAAQ,kBAAkB;AAAA,EAC7E,EAAE;AACJ;AA9De;AAmEf,SAAS,kBAAkB,SAAS,MAAM,QAAQ,aAAa;AAC7D,QAAM,UAAU,CAAC;AAGjB,MAAI,YAAY,oBAAoB,YAAY,iBAAiB,SAAS,GAAG;AAC3E,UAAM,aAAa,OAAO,OAAO,OAAK,KAAK,SAAS,SAAS,EAAE,EAAE,CAAC;AAClE,UAAM,iBAAiB,OAAO,OAAO,OAAK,YAAY,iBAAiB,SAAS,EAAE,EAAE,CAAC;AACrF,UAAM,iBAAiB,WAAW;AAAA,MAAO,QACvC,eAAe,KAAK,QAAM,GAAG,OAAO,GAAG,EAAE;AAAA,IAC3C;AAEA,QAAI,eAAe,SAAS,GAAG;AAC7B,cAAQ,KAAK,2BAA2B,eAAe,IAAI,OAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE;AAAA,IACtF;AAAA,EACF;AAGA,MAAI,QAAQ,gBAAgB,KAAK,gBAAgB,QAAQ,iBAAiB,KAAK,cAAc;AAC3F,YAAQ,KAAK,kCAAkC,QAAQ,YAAY,EAAE;AAAA,EACvE;AAGA,MAAI,KAAK,UAAU;AACjB,YAAQ,KAAK,yBAAyB;AAAA,EACxC;AAGA,MAAI,QAAQ,WAAW,GAAG;AACxB,UAAM,aAAa,OAAO,OAAO,OAAK,KAAK,SAAS,SAAS,EAAE,EAAE,CAAC;AAClE,QAAI,WAAW,SAAS,GAAG;AACzB,cAAQ,KAAK,YAAY,WAAW,IAAI,OAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC,QAAQ;AAAA,IACzE,OAAO;AACL,cAAQ,KAAK,qCAAqC;AAAA,IACpD;AAAA,EACF;AAEA,SAAO;AACT;AArCS;AAuCT,IAAO,0BAAQA;;;AC/Kf,IAAME,OAAM,IAAIC,MAAK;AAGrBD,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,WAAW,MAAM,YAAY,EAAE,IAAI,oBAAoB;AAE7D,QAAI,CAAC,UAAU;AACb,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,WAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAAA,EAClC,SAAS,OAAO;AACd,YAAQ,MAAM,4BAA4B,KAAK;AAC/C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,MAAI;AACF,UAAM,UAAU,MAAM,EAAE,IAAI,KAAK;AAGjC,UAAM,kBAAkB,MAAM,YAAY,EAAE,IAAI,oBAAoB,KAAK;AAAA,MACvE,uBAAuB;AAAA,QACrB,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,MACtB;AAAA,IACF;AAGA,QAAI,QAAQ,uBAAuB;AACjC,YAAM,EAAE,sBAAsB,IAAI;AAElC,UAAI,sBAAsB,qBAAqB,QAAW;AACxD,cAAM,OAAO,SAAS,sBAAsB,gBAAgB;AAC5D,YAAI,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK;AACzC,iBAAO,EAAE,KAAK;AAAA,YACZ,OAAO;AAAA,cACL,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF,GAAG,GAAG;AAAA,QACR;AACA,8BAAsB,mBAAmB;AAAA,MAC3C;AAEA,UAAI,sBAAsB,uBAAuB,QAAW;AAC1D,cAAM,OAAO,SAAS,sBAAsB,kBAAkB;AAC9D,YAAI,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK;AACzC,iBAAO,EAAE,KAAK;AAAA,YACZ,OAAO;AAAA,cACL,MAAM;AAAA,cACN,SAAS;AAAA,YACX;AAAA,UACF,GAAG,GAAG;AAAA,QACR;AACA,8BAAsB,qBAAqB;AAAA,MAC7C;AAGA,UAAI,sBAAsB,oBAAoB,sBAAsB,oBAAoB;AACtF,eAAO,EAAE,KAAK;AAAA,UACZ,OAAO;AAAA,YACL,MAAM;AAAA,YACN,SAAS;AAAA,UACX;AAAA,QACF,GAAG,GAAG;AAAA,MACR;AAAA,IACF;AAEA,UAAM,kBAAkB;AAAA,MACtB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,uBAAuB;AAAA,QACrB,GAAG,gBAAgB;AAAA,QACnB,GAAG,QAAQ;AAAA,MACb;AAAA,IACF;AAEA,UAAM,gBAAgB,MAAME,cAAa,EAAE,IAAI,sBAAsB,eAAe;AACpF,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,4BAA4B,KAAK;AAC/C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAED,IAAO,mBAAQF;;;ACjGf,IAAMG,OAAM,IAAIC,MAAK;AAGrBD,KAAI,IAAI,WAAW,OAAO,MAAM;AAC9B,MAAI;AACF,UAAM,CAAC,UAAU,SAAS,OAAO,QAAQ,UAAU,QAAQ,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC/E,YAAY,EAAE,IAAI,oBAAoB;AAAA,MACtC,WAAW,EAAE,IAAI,oBAAoB;AAAA,MACrC,SAAS,EAAE,IAAI,oBAAoB;AAAA,MACnC,UAAU,EAAE,IAAI,oBAAoB;AAAA,MACpC,YAAY,EAAE,IAAI,oBAAoB;AAAA,MACtC,YAAY,EAAE,IAAI,oBAAoB;AAAA,IACxC,CAAC;AAED,UAAM,aAAa;AAAA,MACjB,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,MACnC,SAAS;AAAA,MACT,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,YAAY;AAAA,UACpB,uBAAuB;AAAA,YACrB,kBAAkB;AAAA,YAClB,oBAAoB;AAAA,UACtB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,IACX,CAAC;AAAA,EACH,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAGDA,KAAI,KAAK,WAAW,OAAO,MAAM;AAC/B,MAAI;AACF,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,UAAM,EAAE,MAAM,UAAU,CAAC,EAAE,IAAI;AAE/B,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,KAAK;AAAA,QACZ,OAAO;AAAA,UACL,MAAM;AAAA,UACN,SAAS;AAAA,QACX;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,UAAM,EAAE,YAAY,MAAM,IAAI;AAC9B,UAAM,UAAU;AAAA,MACd,UAAU,EAAE,UAAU,GAAG,QAAQ,CAAC,EAAE;AAAA,MACpC,SAAS,EAAE,UAAU,GAAG,QAAQ,CAAC,EAAE;AAAA,MACnC,OAAO,EAAE,UAAU,GAAG,QAAQ,CAAC,EAAE;AAAA,MACjC,QAAQ,EAAE,UAAU,GAAG,QAAQ,CAAC,EAAE;AAAA,MAClC,UAAU,EAAE,UAAU,GAAG,QAAQ,CAAC,EAAE;AAAA,IACtC;AAGA,UAAM,CAAC,kBAAkB,iBAAiB,eAAe,cAAc,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC3F,YAAY,EAAE,IAAI,oBAAoB;AAAA,MACtC,WAAW,EAAE,IAAI,oBAAoB;AAAA,MACrC,SAAS,EAAE,IAAI,oBAAoB;AAAA,MACnC,UAAU,EAAE,IAAI,oBAAoB;AAAA,IACtC,CAAC;AAED,UAAM,uBAAuB,IAAI,IAAI,iBAAiB,IAAI,OAAK,EAAE,KAAK,YAAY,CAAC,CAAC;AACpF,UAAM,qBAAqB,IAAI,IAAI,gBAAgB,IAAI,CAAAE,OAAKA,GAAE,KAAK,YAAY,CAAC,CAAC;AACjF,UAAM,qBAAqB,IAAI,IAAI,cAAc,IAAI,OAAK,GAAG,EAAE,MAAM,YAAY,CAAC,IAAI,EAAE,OAAO,YAAY,CAAC,EAAE,CAAC;AAC/G,UAAM,qBAAqB,IAAI,IAAI,eAAe,IAAI,OAAK,EAAE,KAAK,YAAY,CAAC,CAAC;AAGhF,QAAI,KAAK,QAAQ;AACf,iBAAW,aAAa,KAAK,QAAQ;AACnC,YAAI;AACF,cAAI,CAAC,UAAU,MAAM;AACnB,oBAAQ,OAAO,OAAO,KAAK,oBAAoB;AAC/C;AAAA,UACF;AAEA,gBAAM,YAAY,UAAU,KAAK,YAAY;AAG7C,cAAI,CAAC,aAAa,mBAAmB,IAAI,SAAS,GAAG;AACnD,oBAAQ,OAAO,OAAO,KAAK,UAAU,UAAU,IAAI,kBAAkB;AACrE;AAAA,UACF;AAEA,gBAAM,QAAQ;AAAA,YACZ,IAAI,YAAa,UAAU,MAAM,WAAW,IAAK,WAAW;AAAA,YAC5D,MAAM,UAAU;AAAA,YAChB,aAAa,UAAU,eAAe;AAAA,YACtC,cAAc,UAAU,gBAAgB;AAAA,UAC1C;AAEA,gBAAM,UAAU,EAAE,IAAI,sBAAsB,KAAK;AACjD,kBAAQ,OAAO;AAAA,QACjB,SAAS,OAAO;AACd,kBAAQ,OAAO,OAAO,KAAK,0BAA0B,UAAU,IAAI,MAAM,MAAM,OAAO,EAAE;AAAA,QAC1F;AAAA,MACF;AAAA,IACF;AAGA,QAAI,KAAK,SAAS;AAChB,iBAAW,aAAa,KAAK,SAAS;AACpC,YAAI;AACF,cAAI,CAAC,UAAU,MAAM;AACnB,oBAAQ,QAAQ,OAAO,KAAK,oBAAoB;AAChD;AAAA,UACF;AAEA,gBAAM,YAAY,UAAU,KAAK,YAAY;AAG7C,cAAI,CAAC,aAAa,mBAAmB,IAAI,SAAS,GAAG;AACnD,oBAAQ,QAAQ,OAAO,KAAK,UAAU,UAAU,IAAI,kBAAkB;AACtE;AAAA,UACF;AAEA,gBAAM,cAAc;AAAA,YAClB,IAAI,YAAa,UAAU,MAAM,WAAW,IAAK,WAAW;AAAA,YAC5D,MAAM,UAAU;AAAA,YAChB,aAAa,UAAU,eAAe;AAAA,YACtC,YAAY,UAAU,cAAc;AAAA,YACpC,UAAU,UAAU,YAAY;AAAA,YAChC,WAAW,UAAU,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,YACzD,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UACpC;AAEA,gBAAM,UAAU,EAAE,IAAI,sBAAsB,WAAW;AACvD,kBAAQ,QAAQ;AAAA,QAClB,SAAS,OAAO;AACd,kBAAQ,QAAQ,OAAO,KAAK,0BAA0B,UAAU,IAAI,MAAM,MAAM,OAAO,EAAE;AAAA,QAC3F;AAAA,MACF;AAAA,IACF;AAGA,QAAI,KAAK,OAAO;AACd,iBAAW,YAAY,KAAK,OAAO;AACjC,YAAI;AACF,cAAI,CAAC,SAAS,SAAS,CAAC,SAAS,QAAQ;AACvC,oBAAQ,MAAM,OAAO,KAAK,8BAA8B;AACxD;AAAA,UACF;AAEA,gBAAM,UAAU,GAAG,SAAS,MAAM,YAAY,CAAC,IAAI,SAAS,OAAO,YAAY,CAAC;AAGhF,cAAI,CAAC,aAAa,mBAAmB,IAAI,OAAO,GAAG;AACjD,oBAAQ,MAAM,OAAO,KAAK,SAAS,SAAS,KAAK,QAAQ,SAAS,MAAM,iBAAiB;AACzF;AAAA,UACF;AAEA,gBAAM,OAAO;AAAA,YACX,IAAI,YAAa,SAAS,MAAM,WAAW,IAAK,WAAW;AAAA,YAC3D,OAAO,SAAS;AAAA,YAChB,QAAQ,SAAS;AAAA,YACjB,UAAU,SAAS,YAAY,CAAC;AAAA,YAChC,cAAc,SAAS,gBAAgB;AAAA,YACvC,UAAU,SAAS,YAAY;AAAA,UACjC;AAEA,gBAAM,SAAS,EAAE,IAAI,sBAAsB,IAAI;AAC/C,kBAAQ,MAAM;AAAA,QAChB,SAAS,OAAO;AACd,kBAAQ,MAAM,OAAO,KAAK,yBAAyB,SAAS,KAAK,MAAM,MAAM,OAAO,EAAE;AAAA,QACxF;AAAA,MACF;AAAA,IACF;AAGA,QAAI,KAAK,UAAU;AACjB,iBAAW,eAAe,KAAK,UAAU;AACvC,YAAI;AACF,cAAI,CAAC,YAAY,MAAM;AACrB,oBAAQ,SAAS,OAAO,KAAK,sBAAsB;AACnD;AAAA,UACF;AAEA,gBAAM,cAAc,YAAY,KAAK,YAAY;AAGjD,cAAI,CAAC,aAAa,qBAAqB,IAAI,WAAW,GAAG;AACvD,oBAAQ,SAAS,OAAO,KAAK,YAAY,YAAY,IAAI,kBAAkB;AAC3E;AAAA,UACF;AAEA,gBAAM,UAAU;AAAA,YACd,IAAI,YAAa,YAAY,MAAM,WAAW,IAAK,WAAW;AAAA,YAC9D,MAAM,YAAY;AAAA,YAClB,SAAS,YAAY,WAAW;AAAA,YAChC,cAAc,YAAY,gBAAgB;AAAA,YAC1C,cAAc,YAAY,gBAAgB;AAAA,YAC1C,aAAa;AAAA,cACX,kBAAkB,CAAC;AAAA,cACnB,OAAO,CAAC;AAAA,cACR,UAAU,CAAC;AAAA,cACX,gBAAgB,CAAC;AAAA,cACjB,GAAG,YAAY;AAAA,YACjB;AAAA,YACA,iBAAiB,CAAC;AAAA,YAClB,WAAW,YAAY,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,YAC3D,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,UACpC;AAEA,gBAAM,YAAY,EAAE,IAAI,sBAAsB,OAAO;AACrD,kBAAQ,SAAS;AAAA,QACnB,SAAS,OAAO;AACd,kBAAQ,SAAS,OAAO,KAAK,4BAA4B,YAAY,IAAI,MAAM,MAAM,OAAO,EAAE;AAAA,QAChG;AAAA,MACF;AAAA,IACF;AAGA,QAAI,KAAK,UAAU;AACjB,iBAAW,eAAe,KAAK,UAAU;AACvC,YAAI;AACF,cAAI,CAAC,YAAY,aAAa,CAAC,YAAY,QAAQ,CAAC,YAAY,YAAY;AAC1E,oBAAQ,SAAS,OAAO,KAAK,iCAAiC;AAC9D;AAAA,UACF;AAEA,gBAAM,UAAU;AAAA,YACd,IAAI,YAAa,YAAY,MAAM,WAAW,IAAK,WAAW;AAAA,YAC9D,MAAM,YAAY;AAAA,YAClB,QAAQ,YAAY,UAAU;AAAA,YAC9B,WAAW,YAAY;AAAA,YACvB,QAAQ,YAAY;AAAA,YACpB,YAAY,YAAY;AAAA,YACxB,OAAO,YAAY,SAAS;AAAA,YAC5B,aAAa,YAAY,eAAe;AAAA,YACxC,WAAW,YAAY;AAAA,UACzB;AAEA,gBAAM,YAAY,EAAE,IAAI,sBAAsB,OAAO;AACrD,kBAAQ,SAAS;AAAA,QACnB,SAAS,OAAO;AACd,kBAAQ,SAAS,OAAO,KAAK,4BAA4B,MAAM,OAAO,EAAE;AAAA,QAC1E;AAAA,MACF;AAAA,IACF;AAGA,QAAI,KAAK,UAAU;AACjB,UAAI;AACF,cAAM,aAAa,EAAE,IAAI,sBAAsB,KAAK,QAAQ;AAAA,MAC9D,SAAS,OAAO;AACd,gBAAQ,MAAM,6BAA6B,KAAK;AAAA,MAClD;AAAA,IACF;AAEA,UAAM,YAAY,OAAO,OAAO,OAAO,EAAE,KAAK,YAAU,OAAO,OAAO,SAAS,CAAC;AAChF,UAAM,gBAAgB,OAAO,OAAO,OAAO,EAAE,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,UAAU,CAAC;AAE7F,WAAO,EAAE,KAAK;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,8BAA8B,aAAa,WAAW,YAAY,SAAS,OAAO,OAAO,OAAO,EAAE,OAAO,CAAC,KAAK,WAAW,MAAM,OAAO,OAAO,QAAQ,CAAC,CAAC,YAAY,EAAE;AAAA,IACjL,GAAG,YAAY,MAAM,GAAG;AAAA,EAC1B,SAAS,OAAO;AACd,YAAQ,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,KAAK;AAAA,MACZ,OAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AACF,CAAC;AAED,IAAO,eAAQF;;;AC3Rf,IAAMG,OAAM,IAAIC,MAAK;AAErBD,KAAI,IAAI,KAAK,OAAO,CAAC;AACrBA,KAAI,IAAI,KAAK,WAAW,CAAC;AACzBA,KAAI,IAAI,KAAK,KAAK;AAAA,EAChB,QAAQ,wBAAC,WAAW,QAAQ,SAAS,WAAW,KAAK,QAAQ,SAAS,WAAW,IAAI,SAAS,KAAtF;AAAA,EACR,aAAa;AACf,CAAC,CAAC;AAEFA,KAAI,IAAI,WAAW,CAAC,MAAM,EAAE,KAAK;AAAA,EAC/B,QAAQ;AAAA,EACR,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,EAClC,aAAa,EAAE,IAAI,eAAe;AACpC,CAAC,CAAC;AAEFA,KAAI,MAAM,iBAAiB,gBAAa;AACxCA,KAAI,MAAM,gBAAgB,eAAW;AACrCA,KAAI,MAAM,cAAc,aAAU;AAClCA,KAAI,MAAM,eAAe,cAAW;AACpCA,KAAI,MAAM,iBAAiB,gBAAa;AACxCA,KAAI,MAAM,wBAAwB,uBAAoB;AACtDA,KAAI,MAAM,iBAAiB,gBAAc;AACzCA,KAAI,MAAM,aAAa,YAAU;AAEjCA,KAAI,IAAI,KAAK,OAAO,MAAM;AACxB,QAAM,MAAM,IAAI,IAAI,EAAE,IAAI,GAAG;AAC7B,QAAM,WAAW,IAAI;AAGrB,MAAI,SAAS,WAAW,OAAO,KAAK,SAAS,WAAW,SAAS,GAAG;AAClE,WAAO,EAAE,SAAS;AAAA,EACpB;AAGA,MAAI;AACF,UAAM,YAAY,aAAa,MAAM,gBAAgB;AACrD,YAAQ,IAAI,sBAAsB,SAAS;AAE3C,UAAM,QAAQ,MAAM,EAAE,IAAI,OAAO,MAAM,IAAI,QAAQ,sBAAsB,SAAS,EAAE,CAAC;AAErF,QAAI,MAAM,IAAI;AACZ,cAAQ,IAAI,kBAAkB,WAAW,gBAAgB,MAAM,MAAM;AAGrE,YAAM,WAAW,IAAI,SAAS,MAAM,MAAM;AAAA,QACxC,QAAQ,MAAM;AAAA,QACd,YAAY,MAAM;AAAA,QAClB,SAAS,MAAM;AAAA,MACjB,CAAC;AAGD,UAAI,SAAS,SAAS,KAAK,GAAG;AAC5B,iBAAS,QAAQ,IAAI,gBAAgB,wBAAwB;AAAA,MAC/D;AAGA,UAAI,aAAa,OAAO,aAAa,eAAe;AAClD,iBAAS,QAAQ,IAAI,iBAAiB,UAAU;AAAA,MAClD,OAAO;AACL,iBAAS,QAAQ,IAAI,iBAAiB,0BAA0B;AAAA,MAClE;AAEA,aAAO;AAAA,IACT,OAAO;AACL,cAAQ,IAAI,oBAAoB,WAAW,WAAW,MAAM,MAAM;AAAA,IACpE;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,wBAAwB,KAAK;AAAA,EAC7C;AAGA,MAAI,aAAa,KAAK;AACpB,QAAI;AACF,YAAM,aAAa,MAAM,EAAE,IAAI,OAAO,MAAM,IAAI,QAAQ,gCAAgC,CAAC;AACzF,UAAI,WAAW,IAAI;AACjB,eAAO,IAAI,SAAS,WAAW,MAAM;AAAA,UACnC,SAAS;AAAA,YACP,gBAAgB;AAAA,YAChB,iBAAiB;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,6BAA6B,KAAK;AAAA,IAClD;AAAA,EACF;AAGA,SAAO,EAAE,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAyBR;AACR,CAAC;AAuBDE,KAAI,QAAQ,CAAC,KAAK,MAAM;AACtB,UAAQ,MAAM,iBAAiB,GAAG;AAClC,SAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,yBAAyB,SAAS,+BAA+B,EAAE,GAAG,GAAG;AAC1G,CAAC;AAEDA,KAAI,SAAS,CAAC,MAAM;AAClB,QAAM,MAAM,IAAI,IAAI,EAAE,IAAI,GAAG;AAC7B,MAAI,IAAI,SAAS,WAAW,OAAO,GAAG;AACpC,WAAO,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,aAAa,SAAS,yBAAyB,EAAE,GAAG,GAAG;AAAA,EACxF;AACA,SAAO,EAAE,KAAK,kBAAkB,EAAE,QAAQ,IAAI,CAAC;AACjD,CAAC;AAED,IAAO,iBAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,QAAI;AAAA,OACD,YAAY;AACX,YAAI;AACF,gBAAM,wBAAwB,IAAI,oBAAoB;AACtD,gBAAM,0BAA0B,IAAI,oBAAoB;AAAA,QAC1D,SAAS,OAAO;AACd,kBAAQ,MAAM,sCAAsC,KAAK;AAAA,QAC3D;AAAA,MACF,GAAG;AAAA,IACL;AAEA,WAAOA,KAAI,MAAM,SAAS,EAAE,GAAG,KAAK,IAAI,CAAC;AAAA,EAC3C;AACF;;;ACzLA,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["raw", "app", "Node", "Node", "<PERSON><PERSON>", "saveSettings", "<PERSON><PERSON>", "app", "<PERSON><PERSON>", "app", "<PERSON><PERSON>", "id", "app", "<PERSON><PERSON>", "app", "<PERSON><PERSON>", "app", "<PERSON><PERSON>", "app", "<PERSON><PERSON>", "saveSettings", "app", "<PERSON><PERSON>", "c", "app", "<PERSON><PERSON>", "app"]}