(()=>{"use strict";var e,t,s,n,r,a,i={3940:function(e,t,s){var n=s(4848),r=s(6540),a=s(5338),i=s(4976),l=s(7767),o=s(340),d=s(3559),c=s(6380),h=s(4622);let x={students:[],classes:[],books:[],genres:[],sessions:[],settings:null,loading:!1,error:null,filters:{students:{},sessions:{},books:{}}},u="SET_LOADING",m="SET_ERROR",A="CLEAR_ERROR",j="SET_STUDENTS",g="ADD_STUDENT",y="UPDATE_STUDENT",p="DELETE_STUDENT",v="SET_CLASSES",f="ADD_CLASS",b="UPDATE_CLASS",S="DELETE_CLASS",k="SET_BOOKS",w="ADD_BOOK",C="UPDATE_BOOK",I="DELETE_BOOK",D="SET_GENRES",E="ADD_GENRE",L="UPDATE_GENRE",T="DELETE_GENRE",R="SET_SESSIONS",z="ADD_SESSION",B="UPDATE_SESSION",P="DELETE_SESSION",N="SET_SETTINGS",O="UPDATE_SETTINGS",W="SET_FILTER",M="CLEAR_FILTERS";function G(e,t){switch(t.type){case u:return{...e,loading:t.payload};case m:return{...e,error:t.payload,loading:!1};case A:return{...e,error:null};case j:return{...e,students:t.payload};case g:return{...e,students:[...e.students,t.payload]};case y:return{...e,students:e.students.map(e=>e.id===t.payload.id?t.payload:e)};case p:return{...e,students:e.students.filter(e=>e.id!==t.payload)};case v:return{...e,classes:t.payload};case f:return{...e,classes:[...e.classes,t.payload]};case b:return{...e,classes:e.classes.map(e=>e.id===t.payload.id?t.payload:e)};case S:return{...e,classes:e.classes.filter(e=>e.id!==t.payload)};case k:return{...e,books:t.payload};case w:return{...e,books:[...e.books,t.payload]};case C:return{...e,books:e.books.map(e=>e.id===t.payload.id?t.payload:e)};case I:return{...e,books:e.books.filter(e=>e.id!==t.payload)};case D:return{...e,genres:t.payload};case E:return{...e,genres:[...e.genres,t.payload]};case L:return{...e,genres:e.genres.map(e=>e.id===t.payload.id?t.payload:e)};case T:return{...e,genres:e.genres.filter(e=>e.id!==t.payload)};case R:return{...e,sessions:t.payload};case z:return{...e,sessions:[...e.sessions,t.payload]};case B:return{...e,sessions:e.sessions.map(e=>e.id===t.payload.id?t.payload:e)};case P:return{...e,sessions:e.sessions.filter(e=>e.id!==t.payload)};case N:return{...e,settings:t.payload};case O:return{...e,settings:{...e.settings,...t.payload}};case W:return{...e,filters:{...e.filters,[t.entity]:{...e.filters[t.entity],...t.payload}}};case M:return{...e,filters:x.filters};default:return e}}let F=(0,r.createContext)();async function H(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=`/api${e}`,n={headers:{"Content-Type":"application/json",...t.headers}},r=await fetch(s,{...n,...t});if(!r.ok){var a;throw Error((null==(a=(await r.json().catch(()=>({}))).error)?void 0:a.message)||`HTTP error! status: ${r.status}`)}return r.json()}function $(e){let{children:t}=e,[s,a]=(0,r.useReducer)(G,x),i={async getStudents(){a({type:u,payload:!0});try{let e=await H("/students");return a({type:j,payload:e.data}),e.data}catch(e){throw a({type:m,payload:e.message}),e}finally{a({type:u,payload:!1})}},async createStudent(e){try{let t=await H("/students",{method:"POST",body:JSON.stringify(e)});return a({type:g,payload:t.data}),t.data}catch(e){throw a({type:m,payload:e.message}),e}},async updateStudent(e,t){try{let s=await H(`/students/${e}`,{method:"PUT",body:JSON.stringify(t)});return a({type:y,payload:s.data}),s.data}catch(e){throw a({type:m,payload:e.message}),e}},async deleteStudent(e){try{await H(`/students/${e}`,{method:"DELETE"}),a({type:p,payload:e})}catch(e){throw a({type:m,payload:e.message}),e}},async bulkImportStudents(e){try{let t=await H("/students/bulk",{method:"POST",body:JSON.stringify({students:e})});return t.data.created.forEach(e=>{a({type:g,payload:e})}),t}catch(e){throw a({type:m,payload:e.message}),e}},async getClasses(){a({type:u,payload:!0});try{let e=await H("/classes");return a({type:v,payload:e.data}),e.data}catch(e){throw a({type:m,payload:e.message}),e}finally{a({type:u,payload:!1})}},async createClass(e){try{let t=await H("/classes",{method:"POST",body:JSON.stringify(e)});return a({type:f,payload:t.data}),t.data}catch(e){throw a({type:m,payload:e.message}),e}},async updateClass(e,t){try{let s=await H(`/classes/${e}`,{method:"PUT",body:JSON.stringify(t)});return a({type:b,payload:s.data}),s.data}catch(e){throw a({type:m,payload:e.message}),e}},async deleteClass(e){try{await H(`/classes/${e}`,{method:"DELETE"}),a({type:S,payload:e})}catch(e){throw a({type:m,payload:e.message}),e}},async getBooks(){a({type:u,payload:!0});try{let e=await H("/books");return a({type:k,payload:e.data}),e.data}catch(e){throw a({type:m,payload:e.message}),e}finally{a({type:u,payload:!1})}},async createBook(e){try{let t=await H("/books",{method:"POST",body:JSON.stringify(e)});return a({type:w,payload:t.data}),t.data}catch(e){throw a({type:m,payload:e.message}),e}},async updateBook(e,t){try{let s=await H(`/books/${e}`,{method:"PUT",body:JSON.stringify(t)});return a({type:C,payload:s.data}),s.data}catch(e){throw a({type:m,payload:e.message}),e}},async deleteBook(e){try{await H(`/books/${e}`,{method:"DELETE"}),a({type:I,payload:e})}catch(e){throw a({type:m,payload:e.message}),e}},async getGenres(){a({type:u,payload:!0});try{let e=await H("/genres");return a({type:D,payload:e.data}),e.data}catch(e){throw a({type:m,payload:e.message}),e}finally{a({type:u,payload:!1})}},async createGenre(e){try{let t=await H("/genres",{method:"POST",body:JSON.stringify(e)});return a({type:E,payload:t.data}),t.data}catch(e){throw a({type:m,payload:e.message}),e}},async updateGenre(e,t){try{let s=await H(`/genres/${e}`,{method:"PUT",body:JSON.stringify(t)});return a({type:L,payload:s.data}),s.data}catch(e){throw a({type:m,payload:e.message}),e}},async deleteGenre(e){try{await H(`/genres/${e}`,{method:"DELETE"}),a({type:T,payload:e})}catch(e){throw a({type:m,payload:e.message}),e}},async getSessions(){a({type:u,payload:!0});try{let e=await H("/sessions");return a({type:R,payload:e.data}),e.data}catch(e){throw a({type:m,payload:e.message}),e}finally{a({type:u,payload:!1})}},async createSession(e){try{let t=await H("/sessions",{method:"POST",body:JSON.stringify(e)});return a({type:z,payload:t.data}),t.data}catch(e){throw a({type:m,payload:e.message}),e}},async updateSession(e,t){try{let s=await H(`/sessions/${e}`,{method:"PUT",body:JSON.stringify(t)});return a({type:B,payload:s.data}),s.data}catch(e){throw a({type:m,payload:e.message}),e}},async deleteSession(e){try{await H(`/sessions/${e}`,{method:"DELETE"}),a({type:P,payload:e})}catch(e){throw a({type:m,payload:e.message}),e}},async getSessionsByStudent(e){try{return(await H(`/sessions/student/${e}`)).data}catch(e){throw a({type:m,payload:e.message}),e}},async getRecommendations(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{return(await H("/recommendations",{method:"POST",body:JSON.stringify({studentId:e,limit:t})})).data}catch(e){throw a({type:m,payload:e.message}),e}},async getSettings(){try{let e=await H("/settings");return a({type:N,payload:e.data}),e.data}catch(e){throw a({type:m,payload:e.message}),e}},async updateSettings(e){try{let t=await H("/settings",{method:"PUT",body:JSON.stringify(e)});return a({type:O,payload:t.data}),t.data}catch(e){throw a({type:m,payload:e.message}),e}},async exportData(){try{return(await H("/data/export")).data}catch(e){throw a({type:m,payload:e.message}),e}},async importData(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await H("/data/import",{method:"POST",body:JSON.stringify({data:e,options:t})})}catch(e){throw a({type:m,payload:e.message}),e}},clearError(){a({type:A})},setFilter(e,t){a({type:W,entity:e,payload:t})},clearFilters(){a({type:M})}};return(0,r.useEffect)(()=>{(async()=>{try{await Promise.all([i.getStudents(),i.getClasses(),i.getBooks(),i.getGenres(),i.getSessions(),i.getSettings()])}catch(e){console.error("Error loading initial data:",e)}})()},[]),(0,n.jsx)(F.Provider,{value:{state:s,dispatch:a,api:i},children:t})}function _(){let e=(0,r.useContext)(F);if(!e)throw Error("useApp must be used within an AppProvider");return e}var U=s(2292),J=s(7714),q=s(1536),Y=s(7292),K=s(4487),V=s(6708),Z=s(4195),Q=s(8649),X=s(5124),ee=s(6046),et=s(7898),es=s(4478),en=s(5502),er=s(5604),ea=s(5554),ei=s(9368),el=s(3430),eo=s(1117),ed=s(8105),ec=s(4248),eh=s(205),ex=s(3108),eu=s(4527),em=s(6453);let eA=[{text:"Dashboard",icon:(0,n.jsx)(ea.A,{}),path:"/"},{text:"Students",icon:(0,n.jsx)(ei.A,{}),path:"/students"},{text:"Reading Sessions",icon:(0,n.jsx)(el.A,{}),path:"/sessions"},{text:"Books",icon:(0,n.jsx)(eo.A,{}),path:"/books"},{text:"Classes",icon:(0,n.jsx)(ed.A,{}),path:"/classes"},{text:"Genres",icon:(0,n.jsx)(ec.A,{}),path:"/genres"},{text:"Recommendations",icon:(0,n.jsx)(ed.A,{}),path:"/recommendations"},{text:"Settings",icon:(0,n.jsx)(eh.A,{}),path:"/settings"}],ej=function(e){var t;let{children:s}=e,a=(0,U.A)(),i=(0,l.Zp)(),o=(0,l.zy)(),{state:d}=_(),[c,x]=(0,r.useState)(!1),u=(0,J.A)(a.breakpoints.down("md")),m=()=>{x(!c)},A=(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(q.A,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",px:[1]},children:[(0,n.jsx)(Y.A,{variant:"h6",noWrap:!0,component:"div",color:"primary",children:"Reading Assistant"}),u&&(0,n.jsx)(K.A,{onClick:m,children:(0,n.jsx)(ex.A,{})})]}),(0,n.jsx)(V.A,{}),(0,n.jsx)(Z.A,{children:eA.map(e=>(0,n.jsx)(Q.Ay,{disablePadding:!0,children:(0,n.jsxs)(X.A,{selected:o.pathname===e.path,onClick:()=>{i(e.path),u&&x(!1)},sx:{"&.Mui-selected":{backgroundColor:a.palette.primary.main,color:"white","& .MuiListItemIcon-root":{color:"white"},"&:hover":{backgroundColor:a.palette.primary.dark}}},children:[(0,n.jsx)(ee.A,{children:e.icon}),(0,n.jsx)(et.A,{primary:e.text})]})},e.text))})]});return(0,n.jsxs)(h.A,{sx:{display:"flex"},children:[(0,n.jsx)(es.A,{position:"fixed",sx:{width:{md:"calc(100% - 280px)"},ml:{md:"280px"}},children:(0,n.jsxs)(q.A,{children:[(0,n.jsx)(K.A,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:m,sx:{mr:2,display:{md:"none"}},children:(0,n.jsx)(eu.A,{})}),(0,n.jsx)(Y.A,{variant:"h6",noWrap:!0,component:"div",children:(null==(t=eA.find(e=>e.path===o.pathname))?void 0:t.text)||"Reading Assistant"})]})}),(0,n.jsxs)(h.A,{component:"nav",sx:{width:{md:280},flexShrink:{md:0}},"aria-label":"navigation",children:[(0,n.jsx)(en.Ay,{variant:"temporary",open:c,onClose:m,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",md:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:280}},children:A}),(0,n.jsx)(en.Ay,{variant:"permanent",sx:{display:{xs:"none",md:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:280}},open:!0,children:A})]}),(0,n.jsxs)(h.A,{component:"main",sx:{flexGrow:1,p:3,width:{md:"calc(100% - 280px)"},mt:8},children:[s,(0,n.jsx)(er.A,{color:"primary","aria-label":"add",sx:{position:"fixed",bottom:16,right:16},onClick:()=>{let e=o.pathname;"/students"===e?i("/students?action=new"):"/sessions"===e?i("/sessions?action=new"):"/books"===e?i("/books?action=new"):"/classes"===e?i("/classes?action=new"):"/genres"===e&&i("/genres?action=new")},children:(0,n.jsx)(em.A,{})})]})]})};var eg=s(6596),ey=s(478),ep=s(5433),ev=s(5663),ef=s(6740),eb=s(560),eS=s(5709),ek=s(537),ew=s(3328),eC=s(5101),eI=s(1192),eD=s(1076);let eE=function(){var e;let t=(0,l.Zp)(),{state:s}=_(),{students:r,sessions:a,books:i,classes:o}=s,d=r.length,c=a.length,x=i.length,u=o.length,m=a.sort((e,t)=>new Date(t.date)-new Date(e.date)).slice(0,5),A=s.settings,j=(null==A||null==(e=A.readingStatusSettings)?void 0:e.needsAttentionDays)||14,g=new Date;g.setDate(g.getDate()-j);let y=r.filter(e=>!e.lastReadDate||new Date(e.lastReadDate)<g),p=r.reduce((e,t)=>{let s=t.readingLevel||"Not Set";return e[s]=(e[s]||0)+1,e},{}),v=new Date().toDateString(),f=a.filter(e=>new Date(e.date).toDateString()===v).length,b=e=>{let{title:t,value:s,icon:r,color:a="primary",subtitle:i}=e;return(0,n.jsx)(eg.A,{children:(0,n.jsx)(ey.A,{children:(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{color:"textSecondary",gutterBottom:!0,variant:"h6",children:t}),(0,n.jsx)(Y.A,{variant:"h4",component:"div",color:`${a}.main`,children:s}),i&&(0,n.jsx)(Y.A,{color:"textSecondary",variant:"body2",children:i})]}),(0,n.jsx)(ep.A,{sx:{bgcolor:`${a}.main`,width:56,height:56},children:r})]})})})},S=e=>new Date(e).toLocaleDateString("en-GB",{day:"numeric",month:"short",year:"numeric"}),k=e=>"school"===e?"primary":"secondary";return(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{variant:"h4",gutterBottom:!0,children:"Dashboard"}),(0,n.jsxs)(ev.A,{container:!0,spacing:3,sx:{mb:4},children:[(0,n.jsx)(ev.A,{item:!0,xs:12,sm:6,md:3,children:(0,n.jsx)(b,{title:"Total Students",value:d,icon:(0,n.jsx)(ei.A,{}),color:"primary"})}),(0,n.jsx)(ev.A,{item:!0,xs:12,sm:6,md:3,children:(0,n.jsx)(b,{title:"Reading Sessions",value:c,icon:(0,n.jsx)(el.A,{}),color:"success",subtitle:`${f} today`})}),(0,n.jsx)(ev.A,{item:!0,xs:12,sm:6,md:3,children:(0,n.jsx)(b,{title:"Books Available",value:x,icon:(0,n.jsx)(ed.A,{}),color:"warning"})}),(0,n.jsx)(ev.A,{item:!0,xs:12,sm:6,md:3,children:(0,n.jsx)(b,{title:"Classes",value:u,icon:(0,n.jsx)(eC.A,{}),color:"info"})})]}),(0,n.jsxs)(ev.A,{container:!0,spacing:3,children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2,children:[(0,n.jsx)(Y.A,{variant:"h6",children:"Students Needing Attention"}),(0,n.jsx)(ef.A,{label:`${y.length} students`,color:"warning",variant:"outlined"})]}),y.length>0?(0,n.jsxs)(Z.A,{dense:!0,children:[y.slice(0,5).map(e=>(0,n.jsxs)(Q.Ay,{children:[(0,n.jsx)(eb.A,{children:(0,n.jsx)(ep.A,{children:(0,n.jsx)(ei.A,{})})}),(0,n.jsx)(et.A,{primary:e.name,secondary:e.lastReadDate?`Last read: ${S(e.lastReadDate)}`:"Never read"}),(0,n.jsx)(eS.A,{children:(0,n.jsx)(K.A,{edge:"end",onClick:()=>t(`/students/${e.id}`),children:(0,n.jsx)(eI.A,{})})})]},e.id)),y.length>5&&(0,n.jsx)(Q.Ay,{children:(0,n.jsx)(et.A,{primary:`... and ${y.length-5} more`,primaryTypographyProps:{color:"textSecondary"}})})]}):(0,n.jsx)(Y.A,{color:"textSecondary",align:"center",py:2,children:"All students are up to date! \uD83C\uDF89"}),(0,n.jsx)(h.A,{mt:2,children:(0,n.jsx)(ek.A,{variant:"outlined",fullWidth:!0,onClick:()=>t("/students"),children:"View All Students"})})]})})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:2,children:[(0,n.jsx)(Y.A,{variant:"h6",children:"Recent Reading Sessions"}),(0,n.jsx)(ef.A,{label:`${m.length} recent`,color:"success",variant:"outlined"})]}),m.length>0?(0,n.jsx)(Z.A,{dense:!0,children:m.map(e=>{var t;return(0,n.jsxs)(Q.Ay,{children:[(0,n.jsx)(eb.A,{children:(0,n.jsx)(ep.A,{sx:{bgcolor:k(e.environment)},children:"school"===e.environment?"S":"H"})}),(0,n.jsx)(et.A,{primary:`${e.bookTitle} by ${e.author}`,secondary:(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{variant:"body2",component:"span",children:null==(t=r.find(t=>t.id===e.studentId))?void 0:t.name}),(0,n.jsx)("br",{}),(0,n.jsxs)(Y.A,{variant:"caption",color:"textSecondary",children:[S(e.date)," • ",e.assessment]})]})}),(0,n.jsx)(eS.A,{children:(0,n.jsx)(ef.A,{label:e.environment,size:"small",color:k(e.environment),variant:"outlined"})})]},e.id)})}):(0,n.jsx)(Y.A,{color:"textSecondary",align:"center",py:2,children:"No reading sessions recorded yet"}),(0,n.jsx)(h.A,{mt:2,children:(0,n.jsx)(ek.A,{variant:"outlined",fullWidth:!0,onClick:()=>t("/sessions"),children:"View All Sessions"})})]})})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(Y.A,{variant:"h6",gutterBottom:!0,children:"Reading Level Distribution"}),Object.keys(p).length>0?(0,n.jsx)(h.A,{children:Object.entries(p).map(e=>{let[t,s]=e;return(0,n.jsxs)(h.A,{mb:2,children:[(0,n.jsxs)(h.A,{display:"flex",justifyContent:"space-between",mb:1,children:[(0,n.jsx)(Y.A,{variant:"body2",children:t}),(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:[s," students"]})]}),(0,n.jsx)(ew.A,{variant:"determinate",value:s/d*100,sx:{height:8,borderRadius:4}})]},t)})}):(0,n.jsx)(Y.A,{color:"textSecondary",align:"center",py:2,children:"No reading levels set yet"})]})})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(Y.A,{variant:"h6",gutterBottom:!0,children:"Quick Actions"}),(0,n.jsxs)(ev.A,{container:!0,spacing:2,children:[(0,n.jsx)(ev.A,{item:!0,xs:6,children:(0,n.jsx)(ek.A,{variant:"outlined",fullWidth:!0,startIcon:(0,n.jsx)(ei.A,{}),onClick:()=>t("/students?action=new"),children:"Add Student"})}),(0,n.jsx)(ev.A,{item:!0,xs:6,children:(0,n.jsx)(ek.A,{variant:"outlined",fullWidth:!0,startIcon:(0,n.jsx)(el.A,{}),onClick:()=>t("/sessions?action=new"),children:"Log Session"})}),(0,n.jsx)(ev.A,{item:!0,xs:6,children:(0,n.jsx)(ek.A,{variant:"outlined",fullWidth:!0,startIcon:(0,n.jsx)(ed.A,{}),onClick:()=>t("/books?action=new"),children:"Add Book"})}),(0,n.jsx)(ev.A,{item:!0,xs:6,children:(0,n.jsx)(ek.A,{variant:"outlined",fullWidth:!0,startIcon:(0,n.jsx)(eD.A,{}),onClick:()=>t("/recommendations"),children:"Get Recommendations"})})]})]})})})]})]})};var eL=s(6976),eT=s(1295),eR=s(4511),ez=s(3459),eB=s(3988),eP=s(4372),eN=s(9677),eO=s(5976),eW=s(730),eM=s(2253),eG=s(9793),eF=s(6985),eH=s(4095),e$=s(9828),e_=s(1792),eU=s(739),eJ=s(2475),eq=s(9318),eY=s(5028),eK=s(832),eV=s(9827);let eZ=function(){let e=(0,l.Zp)(),{state:t,api:s}=_(),{students:a,classes:i,genres:o}=t,[d,c]=(0,r.useState)(""),[x,u]=(0,r.useState)(""),[m,A]=(0,r.useState)(!1),[j,g]=(0,r.useState)(null),[y,p]=(0,r.useState)({open:!1,message:"",severity:"success"}),[v,f]=(0,r.useState)({name:"",classId:"",readingLevel:"",preferences:{favoriteGenreIds:[],likes:[],dislikes:[],readingFormats:[]}}),[b,S]=(0,r.useState)(!1),[k,w]=(0,r.useState)(""),[C,I]=(0,r.useState)(!1),[D,E]=(0,r.useState)(""),L=a.filter(e=>{let t=e.name.toLowerCase().includes(d.toLowerCase()),s=!x||e.classId===x;return t&&s}),T=async e=>{if(window.confirm("Are you sure you want to delete this student? This action cannot be undone."))try{await s.deleteStudent(e),p({open:!0,message:"Student deleted successfully",severity:"success"})}catch(e){p({open:!0,message:"Failed to delete student",severity:"error"})}},R=async()=>{try{j?(await s.updateStudent(j.id,v),p({open:!0,message:"Student updated successfully",severity:"success"})):(await s.createStudent(v),p({open:!0,message:"Student created successfully",severity:"success"})),A(!1)}catch(e){p({open:!0,message:"Failed to save student",severity:"error"})}},z=()=>{A(!1),g(null)},B=()=>{p({...y,open:!1})},P=()=>{S(!1),w(""),E("")},N=async()=>{if(!k.trim())return void p({open:!0,message:"Please enter student data",severity:"error"});I(!0);try{let e;try{e=JSON.parse(k)}catch{e=O(k)}if(!Array.isArray(e)||0===e.length)return void p({open:!0,message:"No valid student data found",severity:"error"});let t=await s.bulkImportStudents(e),n=t.data.created.length,r=t.data.errors.length;p({open:!0,message:`Successfully imported ${n} students${r>0?`, ${r} errors`:""}`,severity:r>0?"warning":"success"}),P()}catch(e){p({open:!0,message:"Failed to import students",severity:"error"})}finally{I(!1)}},O=e=>{let t=e.trim().split("\n").filter(e=>e.trim()),s=[];for(let e of t){let t=e.trim();t&&s.push({name:t,classId:D||null,readingLevel:null,preferences:{favoriteGenreIds:[],likes:[],dislikes:[],readingFormats:[]}})}return s},W=e=>i.find(t=>t.id===e);return(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,n.jsx)(Y.A,{variant:"h4",children:"Students"}),(0,n.jsxs)(h.A,{display:"flex",gap:2,children:[(0,n.jsx)(ek.A,{variant:"outlined",startIcon:(0,n.jsx)(em.A,{}),onClick:()=>{S(!0),E("")},children:"Bulk Import"}),(0,n.jsx)(ek.A,{variant:"contained",startIcon:(0,n.jsx)(em.A,{}),onClick:()=>{g(null),f({name:"",classId:"",readingLevel:"",preferences:{favoriteGenreIds:[],likes:[],dislikes:[],readingFormats:[]}}),A(!0)},children:"Add Student"})]})]}),(0,n.jsx)(eg.A,{sx:{mb:3},children:(0,n.jsx)(ey.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,alignItems:"center",children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Search students",value:d,onChange:e=>c(e.target.value),InputProps:{startAdornment:(0,n.jsx)(eY.A,{color:"action",sx:{mr:1}})}})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Filter by Class"}),(0,n.jsxs)(ez.A,{value:x,onChange:e=>u(e.target.value),label:"Filter by Class",sx:{fontSize:"1.4rem",minHeight:64,height:64,"& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiMenuItem-root":{fontSize:"1.4rem",minHeight:56,padding:"12px 16px"},"& .MuiSelect-icon":{fontSize:"2rem"}},children:[(0,n.jsx)(eB.A,{value:"",children:"All Classes"}),i.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))]})]})})]})})}),(0,n.jsx)(eP.A,{component:eN.A,children:(0,n.jsxs)(eO.A,{children:[(0,n.jsx)(eW.A,{children:(0,n.jsxs)(eM.A,{children:[(0,n.jsx)(eG.A,{children:"Name"}),(0,n.jsx)(eG.A,{children:"Class"}),(0,n.jsx)(eG.A,{children:"Reading Level"}),(0,n.jsx)(eG.A,{children:"Last Read"}),(0,n.jsx)(eG.A,{children:"Favorite Genres"}),(0,n.jsx)(eG.A,{align:"right",children:"Actions"})]})}),(0,n.jsx)(eF.A,{children:L.map(t=>{var s;return(0,n.jsxs)(eM.A,{children:[(0,n.jsx)(eG.A,{children:(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",children:[(0,n.jsx)(ep.A,{sx:{bgcolor:"primary.main",mr:2,width:32,height:32},children:(0,n.jsx)(ei.A,{})}),(0,n.jsx)(Y.A,{variant:"body1",fontWeight:"medium",children:t.name})]})}),(0,n.jsx)(eG.A,{children:W(t.classId)?(0,n.jsx)(ef.A,{label:W(t.classId).name,size:"small",color:"secondary",variant:"outlined"}):"-"}),(0,n.jsx)(eG.A,{children:t.readingLevel&&(0,n.jsx)(ef.A,{label:t.readingLevel,size:"small",color:(s=t.readingLevel)&&"Not Set"!==s?s.toLowerCase().includes("beginner")||s.toLowerCase().includes("early")?"success":s.toLowerCase().includes("intermediate")||s.toLowerCase().includes("developing")?"warning":s.toLowerCase().includes("advanced")||s.toLowerCase().includes("fluent")?"primary":"default":"default",variant:"outlined"})}),(0,n.jsx)(eG.A,{children:t.lastReadDate?new Date(t.lastReadDate).toLocaleDateString():"Never"}),(0,n.jsx)(eG.A,{children:(0,n.jsxs)(h.A,{display:"flex",gap:.5,flexWrap:"wrap",children:[t.preferences.favoriteGenreIds.slice(0,3).map(e=>{let t=o.find(t=>t.id===e);return t?(0,n.jsx)(ef.A,{label:t.name,size:"small",variant:"outlined"},e):null}),t.preferences.favoriteGenreIds.length>3&&(0,n.jsx)(ef.A,{label:`+${t.preferences.favoriteGenreIds.length-3} more`,size:"small",variant:"outlined"}),0===t.preferences.favoriteGenreIds.length&&(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"None set"})]})}),(0,n.jsxs)(eG.A,{align:"right",children:[(0,n.jsx)(ek.A,{size:"small",onClick:()=>e(`/students/${t.id}`),sx:{mr:1},children:"View"}),(0,n.jsx)(K.A,{size:"small",onClick:()=>{g(t),f({name:t.name,classId:t.classId||"",readingLevel:t.readingLevel||"",preferences:{...t.preferences}}),A(!0)},children:(0,n.jsx)(eK.A,{})}),(0,n.jsx)(K.A,{size:"small",color:"error",onClick:()=>T(t.id),children:(0,n.jsx)(eV.A,{})})]})]},t.id)})})]})}),0===L.length&&(0,n.jsx)(eg.A,{sx:{textAlign:"center",py:8},children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(ei.A,{sx:{fontSize:64,color:"text.secondary",mb:2}}),(0,n.jsx)(Y.A,{variant:"h6",color:"textSecondary",gutterBottom:!0,children:"No students found"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:d||x?"Try adjusting your search or filter criteria":"Get started by adding your first student"})]})}),(0,n.jsxs)(eH.A,{open:m,onClose:z,maxWidth:"sm",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:j?"Edit Student":"Add New Student"}),(0,n.jsx)(e_.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,sx:{mt:1},children:[(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Student Name",value:v.name,onChange:e=>f({...v,name:e.target.value}),required:!0})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Class"}),(0,n.jsxs)(ez.A,{value:v.classId,onChange:e=>f({...v,classId:e.target.value}),label:"Class",sx:{fontSize:"1.4rem",minHeight:64,height:64,"& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiMenuItem-root":{fontSize:"1.4rem",minHeight:56,padding:"12px 16px"},"& .MuiSelect-icon":{fontSize:"2rem"}},children:[(0,n.jsx)(eB.A,{value:"",children:"No Class"}),i.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))]})]})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Reading Level",value:v.readingLevel,onChange:e=>f({...v,readingLevel:e.target.value}),placeholder:"e.g., Beginner, Intermediate, Advanced"})})]})}),(0,n.jsxs)(eU.A,{children:[(0,n.jsx)(ek.A,{onClick:z,children:"Cancel"}),(0,n.jsx)(ek.A,{onClick:R,variant:"contained",disabled:!v.name.trim(),children:j?"Update":"Create"})]})]}),(0,n.jsxs)(eH.A,{open:b,onClose:P,maxWidth:"sm",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:"Bulk Import Students"}),(0,n.jsx)(e_.A,{children:(0,n.jsxs)(h.A,{sx:{mt:1},children:[(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:"Enter one student name per line:"}),(0,n.jsx)(eL.A,{fullWidth:!0,multiline:!0,rows:10,label:"Student Names",value:k,onChange:e=>w(e.target.value),placeholder:"John Doe Jane Smith Bob Johnson ...",variant:"outlined",sx:{mb:3}}),(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Assign to Class"}),(0,n.jsxs)(ez.A,{value:D,onChange:e=>E(e.target.value),label:"Assign to Class",sx:{fontSize:"1.4rem",minHeight:64,height:64,"& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiMenuItem-root":{fontSize:"1.4rem",minHeight:56,padding:"12px 16px"},"& .MuiSelect-icon":{fontSize:"2rem"}},children:[(0,n.jsx)(eB.A,{value:"",children:"No Class Assignment"}),i.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))]})]}),(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",sx:{mt:2},children:["\uD83D\uDCA1 ",(0,n.jsx)("strong",{children:"Tip:"})," Reading levels and preferences can be added later for each student individually."]})]})}),(0,n.jsxs)(eU.A,{children:[(0,n.jsx)(ek.A,{onClick:P,disabled:C,children:"Cancel"}),(0,n.jsx)(ek.A,{onClick:N,variant:"contained",disabled:!k.trim()||C,children:C?"Importing...":`Import ${k.trim().split("\n").filter(e=>e.trim()).length} Students`})]})]}),(0,n.jsx)(eJ.A,{open:y.open,autoHideDuration:4e3,onClose:B,anchorOrigin:{vertical:"bottom",horizontal:"right"},children:(0,n.jsx)(eq.A,{onClose:B,severity:y.severity,children:y.message})})]})};var eQ=s(6796),eX=s(7441);let e0=function(){let{id:e}=(0,l.g)(),t=(0,l.Zp)(),{state:s,api:a}=_(),[i,o]=(0,r.useState)(null),[d,c]=(0,r.useState)([]),[x,u]=(0,r.useState)(!0);console.log("StudentDetailPage - Component rendered with ID:",e),(0,r.useEffect)(()=>{if(console.log("StudentDetailPage - Looking for student with ID:",e),console.log("StudentDetailPage - Available students:",s.students.length),console.log("StudentDetailPage - Available sessions:",s.sessions.length),0===s.students.length)return void console.log("StudentDetailPage - No students loaded yet, waiting...");let t=s.students.find(t=>t.id===e);if(t)if(console.log("StudentDetailPage - Found student:",t),o(t),s.sessions.length>0){let t=s.sessions.filter(t=>t.studentId===e);console.log("StudentDetailPage - Found sessions:",t.length),c(t.sort((e,t)=>new Date(t.date)-new Date(e.date)))}else console.log("StudentDetailPage - No sessions available yet"),c([]);else console.log("StudentDetailPage - Student not found with ID:",e),console.log("StudentDetailPage - Available student IDs:",s.students.map(e=>e.id));u(!1)},[e,s.students,s.sessions]);let m=e=>new Date(e).toLocaleDateString("en-GB",{day:"numeric",month:"short",year:"numeric"});if(x)return(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",gap:2,mb:3,children:[(0,n.jsx)(K.A,{onClick:()=>t("/students"),color:"primary",children:(0,n.jsx)(eQ.A,{})}),(0,n.jsx)(Y.A,{variant:"h4",children:"Student Details"})]}),(0,n.jsx)(ew.A,{}),(0,n.jsx)(Y.A,{variant:"h6",align:"center",sx:{mt:4},children:"Loading student details..."}),(0,n.jsxs)(Y.A,{variant:"body2",align:"center",sx:{mt:2,color:"text.secondary"},children:["Student ID: ",e]})]});if(!i)return(0,n.jsxs)(h.A,{children:[(0,n.jsx)(eq.A,{severity:"error",sx:{mb:2},children:"Student not found"}),(0,n.jsx)(ek.A,{startIcon:(0,n.jsx)(eQ.A,{}),onClick:()=>t("/students"),variant:"outlined",children:"Back to Students"})]});let A=(()=>{if(0===d.length)return 0;let e=[...d].sort((e,t)=>new Date(t.date)-new Date(e.date)),t=0,s=new Date;for(let n of e){let e=new Date(n.date);if(Math.ceil(Math.abs(s-e)/864e5)<=t+1)t++,s=e;else break}return t})(),j=new Set(d.map(e=>e.bookId)).size;return(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",gap:2,mb:3,children:[(0,n.jsx)(K.A,{onClick:()=>t("/students"),color:"primary",children:(0,n.jsx)(eQ.A,{})}),(0,n.jsx)(Y.A,{variant:"h4",children:"Student Details"})]}),(0,n.jsxs)(ev.A,{container:!0,spacing:3,children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:4,children:(0,n.jsx)(eg.A,{children:(0,n.jsx)(ey.A,{children:(0,n.jsxs)(h.A,{display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",children:[(0,n.jsx)(ep.A,{sx:{width:80,height:80,mb:2,bgcolor:"primary.main"},children:(0,n.jsx)(eC.A,{sx:{fontSize:40}})}),(0,n.jsx)(Y.A,{variant:"h5",gutterBottom:!0,children:i.name}),(0,n.jsxs)(h.A,{display:"flex",gap:1,mb:2,flexWrap:"wrap",justifyContent:"center",children:[(0,n.jsx)(ef.A,{label:`Level: ${i.readingLevel||"Not Set"}`,color:{Beginner:"success",Intermediate:"warning",Advanced:"primary","Not Set":"default"}[i.readingLevel]||"default",variant:"outlined"}),(0,n.jsx)(ef.A,{label:`Class: ${i.className||"Not Assigned"}`,variant:"outlined"})]}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",mb:2,children:i.email||"No email provided"}),(0,n.jsx)(ek.A,{variant:"outlined",startIcon:(0,n.jsx)(eK.A,{}),onClick:()=>t(`/students?action=edit&id=${i.id}`),fullWidth:!0,children:"Edit Student"})]})})})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:8,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(Y.A,{variant:"h6",gutterBottom:!0,children:"Reading Statistics"}),(0,n.jsxs)(ev.A,{container:!0,spacing:3,children:[(0,n.jsx)(ev.A,{item:!0,xs:6,sm:3,children:(0,n.jsxs)(h.A,{textAlign:"center",children:[(0,n.jsx)(Y.A,{variant:"h3",color:"primary.main",children:d.length}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"Total Sessions"})]})}),(0,n.jsx)(ev.A,{item:!0,xs:6,sm:3,children:(0,n.jsxs)(h.A,{textAlign:"center",children:[(0,n.jsx)(Y.A,{variant:"h3",color:"success.main",children:j}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"Books Read"})]})}),(0,n.jsx)(ev.A,{item:!0,xs:6,sm:3,children:(0,n.jsxs)(h.A,{textAlign:"center",children:[(0,n.jsx)(Y.A,{variant:"h3",color:"warning.main",children:A}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"Day Streak"})]})}),(0,n.jsx)(ev.A,{item:!0,xs:6,sm:3,children:(0,n.jsxs)(h.A,{textAlign:"center",children:[(0,n.jsx)(Y.A,{variant:"h3",color:"info.main",children:i.lastReadDate?m(i.lastReadDate):"Never"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"Last Read"})]})})]})]})})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",gap:2,mb:2,children:[(0,n.jsx)(el.A,{color:"primary"}),(0,n.jsx)(Y.A,{variant:"h6",children:"Recent Reading Sessions"}),(0,n.jsx)(ef.A,{label:`${d.length} sessions`,color:"primary",size:"small"})]}),d.length>0?(0,n.jsxs)(Z.A,{children:[d.slice(0,10).map((e,t)=>(0,n.jsxs)(r.Fragment,{children:[(0,n.jsxs)(Q.Ay,{children:[(0,n.jsx)(eb.A,{children:(0,n.jsx)(ep.A,{sx:{bgcolor:"school"===e.environment?"primary":"secondary"},children:"school"===e.environment?"S":"H"})}),(0,n.jsx)(et.A,{primary:(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",gap:1,children:[(0,n.jsx)(Y.A,{variant:"subtitle1",children:e.bookTitle}),(0,n.jsx)(ef.A,{label:e.assessment,size:"small",color:"Excellent"===e.assessment?"success":"Good"===e.assessment?"primary":"Needs Improvement"===e.assessment?"warning":"default"})]}),secondary:(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:["by ",e.author]}),(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",gap:1,mt:1,children:[(0,n.jsx)(eX.A,{fontSize:"small",color:"action"}),(0,n.jsxs)(Y.A,{variant:"caption",color:"textSecondary",children:[m(e.date)," • ",e.environment]})]}),e.notes&&(0,n.jsxs)(Y.A,{variant:"body2",sx:{mt:1,fontStyle:"italic"},children:['"',e.notes,'"']})]})})]}),t<d.slice(0,10).length-1&&(0,n.jsx)(V.A,{})]},e.id)),d.length>10&&(0,n.jsx)(Q.Ay,{children:(0,n.jsx)(et.A,{primary:`... and ${d.length-10} more sessions`,primaryTypographyProps:{color:"textSecondary"}})})]}):(0,n.jsxs)(h.A,{textAlign:"center",py:4,children:[(0,n.jsx)(el.A,{sx:{fontSize:48,color:"text.secondary",mb:2}}),(0,n.jsx)(Y.A,{color:"textSecondary",children:"No reading sessions recorded yet"})]})]})})})]})]})};var e1=s(1120),e2=s(7083),e6=s(9277),e4=s(2595),e3=s(631),e5=s(9824),e8=s(9654);let e7=function(){(0,l.Zp)();let{state:e,api:t}=_(),{sessions:s,students:a,books:i}=e,[o,d]=(0,r.useState)(""),[c,x]=(0,r.useState)(""),[u,m]=(0,r.useState)(""),[A,j]=(0,r.useState)(!1),[g,y]=(0,r.useState)(null),[p,v]=(0,r.useState)({open:!1,message:"",severity:"success"}),[f,b]=(0,r.useState)({date:new Date().toISOString().split("T")[0],studentId:"",bookId:"",bookTitle:"",author:"",assessment:"",notes:"",environment:"school",bookPreference:""}),S=s.filter(e=>{var t;let s=""===o||e.bookTitle.toLowerCase().includes(o.toLowerCase())||(null==(t=a.find(t=>t.id===e.studentId))?void 0:t.name.toLowerCase().includes(o.toLowerCase())),n=!c||e.studentId===c,r=!u||e.environment===u;return s&&n&&r}),k=async e=>{if(window.confirm("Are you sure you want to delete this reading session?"))try{await t.deleteSession(e),v({open:!0,message:"Session deleted successfully",severity:"success"})}catch(e){v({open:!0,message:"Failed to delete session",severity:"error"})}},w=async()=>{try{let e={date:f.date,studentId:f.studentId,assessment:f.assessment,notes:f.notes,environment:f.environment,bookPreference:f.bookPreference||null};f.bookId?(e.bookId=f.bookId,e.bookTitle=f.bookTitle,e.author=f.author):f.bookTitle&&f.author&&(e.bookTitle=f.bookTitle,e.author=f.author),g?(await t.updateSession(g.id,e),v({open:!0,message:"Session updated successfully",severity:"success"})):(await t.createSession(e),v({open:!0,message:"Session created successfully",severity:"success"})),j(!1)}catch(e){v({open:!0,message:"Failed to save session",severity:"error"})}},C=()=>{j(!1),y(null)},I=()=>{v({...p,open:!1})},D=e=>"school"===e?"primary":"secondary";return(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,n.jsx)(Y.A,{variant:"h4",children:"Reading Sessions"}),(0,n.jsx)(ek.A,{variant:"contained",startIcon:(0,n.jsx)(em.A,{}),onClick:()=>{y(null),b({date:new Date().toISOString().split("T")[0],studentId:"",bookId:"",bookTitle:"",author:"",assessment:"",notes:"",environment:"school",bookPreference:""}),j(!0)},children:"Log Session"})]}),(0,n.jsx)(eg.A,{sx:{mb:3},children:(0,n.jsx)(ey.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,alignItems:"center",children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:4,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Search sessions",value:o,onChange:e=>d(e.target.value),InputProps:{startAdornment:(0,n.jsx)(eY.A,{color:"action",sx:{mr:1}})}})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:4,children:(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Filter by Student"}),(0,n.jsxs)(ez.A,{value:c,onChange:e=>x(e.target.value),label:"Filter by Student",children:[(0,n.jsx)(eB.A,{value:"",children:"All Students"}),a.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))]})]})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:4,children:(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Environment"}),(0,n.jsxs)(ez.A,{value:u,onChange:e=>m(e.target.value),label:"Environment",sx:{fontSize:"1.4rem",minHeight:64,height:64,"& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiMenuItem-root":{fontSize:"1.4rem",minHeight:56,padding:"12px 16px"},"& .MuiSelect-icon":{fontSize:"2rem"}},children:[(0,n.jsx)(eB.A,{value:"",children:"All Environments"}),(0,n.jsx)(eB.A,{value:"school",children:"School"}),(0,n.jsx)(eB.A,{value:"home",children:"Home"})]})]})})]})})}),(0,n.jsx)(eP.A,{component:eN.A,children:(0,n.jsxs)(eO.A,{children:[(0,n.jsx)(eW.A,{children:(0,n.jsxs)(eM.A,{children:[(0,n.jsx)(eG.A,{children:"Book"}),(0,n.jsx)(eG.A,{children:"Student"}),(0,n.jsx)(eG.A,{children:"Date"}),(0,n.jsx)(eG.A,{children:"Environment"}),(0,n.jsx)(eG.A,{children:"Assessment"}),(0,n.jsx)(eG.A,{children:"Rating"}),(0,n.jsx)(eG.A,{align:"right",children:"Actions"})]})}),(0,n.jsx)(eF.A,{children:S.map(e=>{var t;let s;return(0,n.jsxs)(eM.A,{children:[(0,n.jsx)(eG.A,{children:(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",children:[(0,n.jsx)(ep.A,{sx:{bgcolor:D(e.environment),mr:2,width:32,height:32},children:"school"===e.environment?(0,n.jsx)(ei.A,{}):(0,n.jsx)(e4.A,{})}),(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{variant:"body1",fontWeight:"medium",children:e.bookTitle}),(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:["by ",e.author]})]})]})}),(0,n.jsx)(eG.A,{children:(t=e.studentId,(s=a.find(e=>e.id===t))?s.name:"Unknown Student")}),(0,n.jsx)(eG.A,{children:new Date(e.date).toLocaleDateString()}),(0,n.jsx)(eG.A,{children:(0,n.jsx)(ef.A,{label:e.environment,color:D(e.environment),variant:"outlined",size:"small"})}),(0,n.jsx)(eG.A,{children:(0,n.jsxs)(Y.A,{variant:"body2",children:[e.assessment,e.notes&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("br",{}),(0,n.jsx)(Y.A,{variant:"caption",color:"textSecondary",children:e.notes})]})]})}),(0,n.jsx)(eG.A,{children:e.bookPreference&&(0,n.jsxs)(n.Fragment,{children:["liked"===e.bookPreference&&(0,n.jsx)(ef.A,{icon:(0,n.jsx)(e3.A,{}),label:"Liked",color:"success",variant:"outlined",size:"small"}),"meh"===e.bookPreference&&(0,n.jsx)(ef.A,{icon:(0,n.jsx)(e5.A,{}),label:"Okay",color:"warning",variant:"outlined",size:"small"}),"disliked"===e.bookPreference&&(0,n.jsx)(ef.A,{icon:(0,n.jsx)(e8.A,{}),label:"Disliked",color:"error",variant:"outlined",size:"small"})]})}),(0,n.jsxs)(eG.A,{align:"right",children:[(0,n.jsx)(K.A,{onClick:()=>{y(e),b({date:e.date,studentId:e.studentId,bookId:e.bookId||"",bookTitle:e.bookTitle,author:e.author,assessment:e.assessment,notes:e.notes||"",environment:e.environment,bookPreference:e.bookPreference||""}),j(!0)},children:(0,n.jsx)(eK.A,{})}),(0,n.jsx)(K.A,{color:"error",onClick:()=>k(e.id),children:(0,n.jsx)(eV.A,{})})]})]},e.id)})})]})}),0===S.length&&(0,n.jsx)(eg.A,{sx:{textAlign:"center",py:8},children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(el.A,{sx:{fontSize:64,color:"text.secondary",mb:2}}),(0,n.jsx)(Y.A,{variant:"h6",color:"textSecondary",gutterBottom:!0,children:"No reading sessions found"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:o||c||u?"Try adjusting your search or filter criteria":"Start tracking reading progress by logging your first session"})]})}),(0,n.jsxs)(eH.A,{open:A,onClose:C,maxWidth:"md",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:g?"Edit Reading Session":"Log Reading Session"}),(0,n.jsx)(e_.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,sx:{mt:1},children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,type:"date",label:"Date",value:f.date,onChange:e=>b({...f,date:e.target.value}),InputLabelProps:{shrink:!0},required:!0})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsxs)(eT.A,{fullWidth:!0,required:!0,children:[(0,n.jsx)(eR.A,{children:"Student"}),(0,n.jsx)(ez.A,{value:f.studentId,onChange:e=>b({...f,studentId:e.target.value}),label:"Student",children:a.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))})]})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(e1.A,{options:i,getOptionLabel:e=>`${e.title} by ${e.author}`,renderInput:e=>(0,n.jsx)(eL.A,{...e,label:"Select Book (optional)"}),onChange:(e,t)=>{t&&b({...f,bookId:t.id,bookTitle:t.title,author:t.author})},value:i.find(e=>e.id===f.bookId)||null})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Book Title",value:f.bookTitle,onChange:e=>b({...f,bookTitle:e.target.value}),placeholder:"Enter book title if not in list"})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Author",value:f.author,onChange:e=>b({...f,author:e.target.value}),placeholder:"Enter author name"})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,multiline:!0,rows:3,label:"Assessment",value:f.assessment,onChange:e=>b({...f,assessment:e.target.value}),placeholder:"Describe how the reading session went...",required:!0})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,multiline:!0,rows:2,label:"Notes (optional)",value:f.notes,onChange:e=>b({...f,notes:e.target.value}),placeholder:"Any additional notes..."})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eT.A,{component:"fieldset",children:(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",children:[(0,n.jsx)(Y.A,{sx:{mr:2},children:"Environment:"}),(0,n.jsx)(e2.A,{control:(0,n.jsx)(e6.A,{checked:"home"===f.environment,onChange:e=>b({...f,environment:e.target.checked?"home":"school"})}),label:"home"===f.environment?"Home":"School"})]})})}),(0,n.jsxs)(ev.A,{item:!0,xs:12,children:[(0,n.jsx)(Y.A,{variant:"subtitle2",gutterBottom:!0,children:"How did the child feel about this book? (Optional)"}),(0,n.jsxs)(h.A,{display:"flex",gap:2,justifyContent:"center",children:[(0,n.jsx)(ek.A,{variant:"liked"===f.bookPreference?"contained":"outlined",color:"success",startIcon:(0,n.jsx)(e3.A,{}),onClick:()=>b({...f,bookPreference:"liked"===f.bookPreference?"":"liked"}),sx:{minWidth:120},children:"Liked"}),(0,n.jsx)(ek.A,{variant:"meh"===f.bookPreference?"contained":"outlined",color:"warning",startIcon:(0,n.jsx)(e5.A,{}),onClick:()=>b({...f,bookPreference:"meh"===f.bookPreference?"":"meh"}),sx:{minWidth:120},children:"Okay"}),(0,n.jsx)(ek.A,{variant:"disliked"===f.bookPreference?"contained":"outlined",color:"error",startIcon:(0,n.jsx)(e8.A,{}),onClick:()=>b({...f,bookPreference:"disliked"===f.bookPreference?"":"disliked"}),sx:{minWidth:120},children:"Disliked"})]})]})]})}),(0,n.jsxs)(eU.A,{children:[(0,n.jsx)(ek.A,{onClick:C,children:"Cancel"}),(0,n.jsx)(ek.A,{onClick:w,variant:"contained",disabled:!f.studentId||!f.assessment,children:g?"Update":"Log Session"})]})]}),(0,n.jsx)(eJ.A,{open:p.open,autoHideDuration:4e3,onClose:I,anchorOrigin:{vertical:"bottom",horizontal:"right"},children:(0,n.jsx)(eq.A,{onClose:I,severity:p.severity,children:p.message})})]})},e9=function(){let{state:e,api:t}=_(),{books:s,genres:a}=e;console.log("BooksPage rendered with:",{booksCount:s.length,genresCount:a.length});let[i,l]=(0,r.useState)(""),[o,d]=(0,r.useState)(""),[c,x]=(0,r.useState)(!1),[u,m]=(0,r.useState)(null),[A,j]=(0,r.useState)({open:!1,message:"",severity:"success"}),[g,y]=(0,r.useState)({title:"",author:"",genreIds:[],readingLevel:"",ageRange:""}),[p,v]=(0,r.useState)(!1),[f,b]=(0,r.useState)(""),[S,k]=(0,r.useState)([]),[w,C]=(0,r.useState)(!1),[I,D]=(0,r.useState)(null),[E,L]=(0,r.useState)(!1),[T,R]=(0,r.useState)(!1),[z,B]=(0,r.useState)([]),[P,N]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=async()=>{try{await Promise.all([t.getBooks(),t.getGenres()])}catch(e){console.error("Error loading books data:",e),j({open:!0,message:"Failed to load books data",severity:"error"})}};0===s.length&&0===a.length&&e()},[t,s.length,a.length]);let O=s.filter(e=>{let t=e.title.toLowerCase().includes(i.toLowerCase())||e.author&&e.author.toLowerCase().includes(i.toLowerCase()),s=!o||e.genreIds.includes(o);return t&&s}),W=async e=>{if(window.confirm("Are you sure you want to delete this book? This action cannot be undone."))try{await t.deleteBook(e),j({open:!0,message:"Book deleted successfully",severity:"success"})}catch(e){j({open:!0,message:"Failed to delete book",severity:"error"})}},M=async()=>{try{u?(await t.updateBook(u.id,g),j({open:!0,message:"Book updated successfully",severity:"success"})):(await t.createBook(g),j({open:!0,message:"Book created successfully",severity:"success"})),x(!1)}catch(e){j({open:!0,message:"Failed to save book",severity:"error"})}},G=()=>{x(!1),m(null)},F=()=>{j({...A,open:!1})},H=()=>{v(!1),b(""),k([])},$=async()=>{if(f.trim()){C(!0);try{let t=await fetch(`/api/books/search/external?q=${encodeURIComponent(f.trim())}&limit=20`),s=await t.json();if(t.ok)k(s.data);else{var e;j({open:!0,message:(null==(e=s.error)?void 0:e.message)||"Search failed",severity:"error"})}}catch(e){j({open:!0,message:"Failed to search external library",severity:"error"})}finally{C(!1)}}},U=async e=>{try{let s=await fetch(`/api/books/external/${e.externalId}`),n=await s.json();if(s.ok)D({...e,...n.data}),L(!0);else{var t;j({open:!0,message:(null==(t=n.error)?void 0:t.message)||"Failed to load book details",severity:"error"})}}catch(e){j({open:!0,message:"Failed to load book details",severity:"error"})}},J=async e=>{try{let n=await fetch("/api/books/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({externalId:e.externalId,genreIds:[],readingLevel:"",ageRange:""})}),r=await n.json();if(n.ok)j({open:!0,message:"Book imported successfully",severity:"success"}),L(!1),await t.getBooks();else{var s;j({open:!0,message:(null==(s=r.error)?void 0:s.message)||"Failed to import book",severity:"error"})}}catch(e){j({open:!0,message:"Failed to import book",severity:"error"})}},q=async()=>{if(g.title.trim()){R(!0),N(!0),B([]);try{let e=await fetch(`/api/books/search/external?q=${encodeURIComponent(g.title.trim())}&limit=10`),t=await e.json();if(e.ok){let e=[],s=new Set;t.data.forEach(t=>{t.author&&!s.has(t.author.toLowerCase())&&(s.add(t.author.toLowerCase()),e.push({name:t.author,bookTitle:t.title,source:"openlibrary"}))}),B(e)}else j({open:!0,message:"Failed to search for authors",severity:"error"}),R(!1)}catch(e){j({open:!0,message:"Failed to search for authors",severity:"error"}),R(!1)}finally{N(!1)}}},V=()=>{R(!1),B([])};return e.loading&&0===s.length?(0,n.jsx)(h.A,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",children:(0,n.jsx)(Y.A,{children:"Loading books..."})}):e.error&&0===s.length?(0,n.jsx)(h.A,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"400px",children:(0,n.jsxs)(h.A,{textAlign:"center",children:[(0,n.jsx)(Y.A,{variant:"h6",color:"error",gutterBottom:!0,children:"Error Loading Books"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:e.error}),(0,n.jsx)(ek.A,{variant:"contained",onClick:()=>window.location.reload(),sx:{mt:2},children:"Retry"})]})}):(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,n.jsx)(Y.A,{variant:"h4",children:"Books"}),(0,n.jsxs)(h.A,{display:"flex",gap:2,children:[(0,n.jsx)(ek.A,{variant:"outlined",startIcon:(0,n.jsx)(eY.A,{}),onClick:()=>{v(!0),b(""),k([])},children:"Search Library"}),(0,n.jsx)(ek.A,{variant:"contained",startIcon:(0,n.jsx)(em.A,{}),onClick:()=>{m(null),y({title:"",author:"",genreIds:[],readingLevel:"",ageRange:""}),x(!0)},children:"Add Book"})]})]}),(0,n.jsx)(eg.A,{sx:{mb:3},children:(0,n.jsx)(ey.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,alignItems:"center",children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Search books",value:i,onChange:e=>l(e.target.value),InputProps:{startAdornment:(0,n.jsx)(eY.A,{color:"action",sx:{mr:1}})}})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Filter by Genre"}),(0,n.jsxs)(ez.A,{value:o,onChange:e=>d(e.target.value),label:"Filter by Genre",sx:{fontSize:"1.4rem",minHeight:64,height:64,"& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiMenuItem-root":{fontSize:"1.4rem",minHeight:56,padding:"12px 16px"},"& .MuiSelect-icon":{fontSize:"2rem"}},children:[(0,n.jsx)(eB.A,{value:"",children:"All Genres"}),a.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))]})]})})]})})}),(0,n.jsx)(eP.A,{component:eN.A,children:(0,n.jsxs)(eO.A,{children:[(0,n.jsx)(eW.A,{children:(0,n.jsxs)(eM.A,{children:[(0,n.jsx)(eG.A,{children:"Title"}),(0,n.jsx)(eG.A,{children:"Author"}),(0,n.jsx)(eG.A,{children:"Level"}),(0,n.jsx)(eG.A,{children:"Age Range"}),(0,n.jsx)(eG.A,{children:"Genres"}),(0,n.jsx)(eG.A,{align:"right",children:"Actions"})]})}),(0,n.jsx)(eF.A,{children:O.map(e=>{let t;return(0,n.jsxs)(eM.A,{children:[(0,n.jsx)(eG.A,{children:(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",children:[e.coverImage?(0,n.jsx)("img",{src:e.coverImage,alt:`${e.title} cover`,style:{width:40,height:56,objectFit:"cover",borderRadius:4,marginRight:12,border:"1px solid #e0e0e0"}}):(0,n.jsx)(eo.A,{sx:{fontSize:32,color:"primary.main",mr:1}}),(0,n.jsx)(Y.A,{variant:"body1",fontWeight:"medium",children:e.title})]})}),(0,n.jsx)(eG.A,{children:e.author||"-"}),(0,n.jsx)(eG.A,{children:e.readingLevel&&(0,n.jsx)(ef.A,{label:e.readingLevel,size:"small",color:"primary",variant:"outlined"})}),(0,n.jsx)(eG.A,{children:e.ageRange&&(0,n.jsx)(ef.A,{label:e.ageRange,size:"small",color:"secondary",variant:"outlined"})}),(0,n.jsx)(eG.A,{children:(0,n.jsx)(h.A,{display:"flex",gap:.5,flexWrap:"wrap",children:(t=e.genreIds,a.filter(e=>t.includes(e.id))).map(e=>(0,n.jsx)(ef.A,{label:e.name,size:"small",variant:"outlined"},e.id))})}),(0,n.jsxs)(eG.A,{align:"right",children:[(0,n.jsx)(K.A,{onClick:()=>{m(e),y({title:e.title,author:e.author,genreIds:e.genreIds||[],readingLevel:e.readingLevel||"",ageRange:e.ageRange||""}),x(!0)},children:(0,n.jsx)(eK.A,{})}),(0,n.jsx)(K.A,{color:"error",onClick:()=>W(e.id),children:(0,n.jsx)(eV.A,{})})]})]},e.id)})})]})}),0===O.length&&(0,n.jsx)(eg.A,{sx:{textAlign:"center",py:8},children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(eo.A,{sx:{fontSize:64,color:"text.secondary",mb:2}}),(0,n.jsx)(Y.A,{variant:"h6",color:"textSecondary",gutterBottom:!0,children:"No books found"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:i||o?"Try adjusting your search or filter criteria":"Start building your library by adding your first book"})]})}),(0,n.jsxs)(eH.A,{open:c,onClose:G,maxWidth:"sm",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:u?"Edit Book":"Add New Book"}),(0,n.jsx)(e_.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,sx:{mt:1},children:[(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Book Title",value:g.title,onChange:e=>y({...g,title:e.target.value}),required:!0})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsxs)(h.A,{display:"flex",gap:1,children:[(0,n.jsx)(eL.A,{fullWidth:!0,label:"Author (optional)",value:g.author,onChange:e=>y({...g,author:e.target.value})}),(0,n.jsx)(ek.A,{variant:"outlined",onClick:q,disabled:!g.title.trim(),sx:{minWidth:"auto",px:2},children:(0,n.jsx)(eY.A,{})})]})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Genres"}),(0,n.jsx)(ez.A,{multiple:!0,value:g.genreIds,onChange:e=>y({...g,genreIds:e.target.value}),label:"Genres",sx:{fontSize:"1.4rem",minHeight:64,height:64,"& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiMenuItem-root":{fontSize:"1.4rem",minHeight:56,padding:"12px 16px"},"& .MuiSelect-icon":{fontSize:"2rem"}},renderValue:e=>(0,n.jsx)(h.A,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:e.map(e=>{let t=a.find(t=>t.id===e);return t?(0,n.jsx)(ef.A,{label:t.name,size:"small"},e):null})}),children:a.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))})]})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Reading Level",value:g.readingLevel,onChange:e=>y({...g,readingLevel:e.target.value}),placeholder:"e.g., Beginner, Intermediate, Advanced"})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Age Range",value:g.ageRange,onChange:e=>y({...g,ageRange:e.target.value}),placeholder:"e.g., 5-7 years, 8-10 years"})})]})}),(0,n.jsxs)(eU.A,{children:[(0,n.jsx)(ek.A,{onClick:G,children:"Cancel"}),(0,n.jsx)(ek.A,{onClick:M,variant:"contained",disabled:!g.title.trim(),children:u?"Update":"Add Book"})]})]}),(0,n.jsxs)(eH.A,{open:p,onClose:H,maxWidth:"md",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:"Search External Library"}),(0,n.jsxs)(e_.A,{children:[(0,n.jsxs)(h.A,{sx:{mb:3,display:"flex",gap:1},children:[(0,n.jsx)(eL.A,{fullWidth:!0,label:"Search for books by title or author",value:f,onChange:e=>b(e.target.value),onKeyPress:e=>"Enter"===e.key&&$(),placeholder:"e.g., Harry Potter, J.K. Rowling"}),(0,n.jsx)(ek.A,{variant:"contained",onClick:$,disabled:w||!f.trim(),children:w?"Searching...":"Search"})]}),S.length>0&&(0,n.jsx)(Z.A,{sx:{maxHeight:400,overflow:"auto"},children:S.map((e,t)=>(0,n.jsx)(Q.Ay,{divider:!0,children:(0,n.jsxs)(h.A,{sx:{display:"flex",alignItems:"center",width:"100%"},children:[e.coverImage&&(0,n.jsx)("img",{src:e.coverImage,alt:e.title,style:{width:50,height:70,objectFit:"cover",marginRight:16}}),(0,n.jsxs)(h.A,{sx:{flexGrow:1},children:[(0,n.jsx)(Y.A,{variant:"subtitle1",gutterBottom:!0,children:e.title}),(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:["by ",e.author]}),e.firstPublishYear&&(0,n.jsxs)(Y.A,{variant:"caption",color:"textSecondary",children:["First published: ",e.firstPublishYear]})]}),(0,n.jsxs)(h.A,{sx:{display:"flex",gap:1},children:[(0,n.jsx)(ek.A,{size:"small",variant:"contained",onClick:()=>J(e),children:"Add to Library"}),(0,n.jsx)(ek.A,{size:"small",onClick:()=>U(e),children:"Preview"})]})]})},t))})]}),(0,n.jsx)(eU.A,{children:(0,n.jsx)(ek.A,{onClick:H,children:"Close"})})]}),(0,n.jsxs)(eH.A,{open:E,onClose:()=>L(!1),maxWidth:"sm",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:"Book Preview"}),(0,n.jsx)(e_.A,{children:I&&(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{sx:{display:"flex",gap:2,mb:3},children:[I.coverImage&&(0,n.jsx)("img",{src:I.coverImage,alt:I.title,style:{width:120,height:160,objectFit:"cover"}}),(0,n.jsxs)(h.A,{sx:{flexGrow:1},children:[(0,n.jsx)(Y.A,{variant:"h6",gutterBottom:!0,children:I.title}),(0,n.jsxs)(Y.A,{variant:"subtitle1",color:"textSecondary",gutterBottom:!0,children:["by ",I.author]}),I.firstPublishYear&&(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:["First published: ",I.firstPublishYear]}),I.pageCount&&(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:["Pages: ",I.pageCount]})]})]}),I.description&&(0,n.jsxs)(h.A,{sx:{mb:3},children:[(0,n.jsx)(Y.A,{variant:"subtitle2",gutterBottom:!0,children:"Description"}),(0,n.jsx)(Y.A,{variant:"body2",children:I.description})]}),I.subjects&&I.subjects.length>0&&(0,n.jsxs)(h.A,{sx:{mb:3},children:[(0,n.jsx)(Y.A,{variant:"subtitle2",gutterBottom:!0,children:"Subjects"}),(0,n.jsx)(h.A,{sx:{display:"flex",gap:.5,flexWrap:"wrap"},children:I.subjects.slice(0,8).map((e,t)=>(0,n.jsx)(ef.A,{label:e,size:"small",variant:"outlined"},t))})]}),I.authorBio&&(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{variant:"subtitle2",gutterBottom:!0,children:"About the Author"}),(0,n.jsx)(Y.A,{variant:"body2",children:I.authorBio})]})]})}),(0,n.jsxs)(eU.A,{children:[(0,n.jsx)(ek.A,{onClick:()=>L(!1),children:"Close"}),(0,n.jsx)(ek.A,{variant:"contained",onClick:()=>J(I),children:"Import to Library"})]})]}),(0,n.jsxs)(eH.A,{open:T,onClose:V,maxWidth:"sm",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:"Select Author"}),(0,n.jsxs)(e_.A,{children:[(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",gutterBottom:!0,sx:{mb:2},children:['Found potential authors for "',g.title,'":']}),P?(0,n.jsx)(h.A,{display:"flex",justifyContent:"center",p:3,children:(0,n.jsx)(Y.A,{children:"Searching for authors..."})}):z.length>0?(0,n.jsx)(Z.A,{children:z.map((e,t)=>(0,n.jsxs)(Q.Ay,{divider:!0,children:[(0,n.jsx)(et.A,{primary:e.name,secondary:`From: ${e.bookTitle}`}),(0,n.jsx)(eS.A,{children:(0,n.jsx)(ek.A,{size:"small",variant:"contained",onClick:()=>{var t;return t=e.name,void(y({...g,author:t}),R(!1),B([]))},children:"Select"})})]},t))}):(0,n.jsx)(h.A,{textAlign:"center",p:3,children:(0,n.jsx)(Y.A,{color:"textSecondary",children:"No authors found for this title."})})]}),(0,n.jsx)(eU.A,{children:(0,n.jsx)(ek.A,{onClick:V,children:"Cancel"})})]}),(0,n.jsx)(eJ.A,{open:A.open,autoHideDuration:4e3,onClose:F,anchorOrigin:{vertical:"bottom",horizontal:"right"},children:(0,n.jsx)(eq.A,{onClose:F,severity:A.severity,children:A.message})})]})};var te=s(9306);let tt=function(){let{state:e,api:t}=_(),{classes:s}=e,[a,i]=(0,r.useState)(!1),[l,o]=(0,r.useState)(null),[d,c]=(0,r.useState)({open:!1,message:"",severity:"success"}),[x,u]=(0,r.useState)({name:"",teacherName:"",schoolYear:""}),m=async e=>{if(window.confirm("Are you sure you want to delete this class?"))try{await t.deleteClass(e),c({open:!0,message:"Class deleted successfully",severity:"success"})}catch(e){c({open:!0,message:"Failed to delete class",severity:"error"})}},A=async()=>{try{l?(await t.updateClass(l.id,x),c({open:!0,message:"Class updated successfully",severity:"success"})):(await t.createClass(x),c({open:!0,message:"Class created successfully",severity:"success"})),i(!1)}catch(e){c({open:!0,message:"Failed to save class",severity:"error"})}},j=()=>{i(!1),o(null)},g=()=>{c({...d,open:!1})};return(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,n.jsx)(Y.A,{variant:"h4",children:"Classes"}),(0,n.jsx)(ek.A,{variant:"contained",startIcon:(0,n.jsx)(em.A,{}),onClick:()=>{o(null),u({name:"",teacherName:"",schoolYear:""}),i(!0)},children:"Add Class"})]}),(0,n.jsx)(ev.A,{container:!0,spacing:3,children:s.map(e=>(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,lg:4,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",mb:2,children:[(0,n.jsx)(te.A,{sx:{fontSize:40,color:"primary.main",mr:2}}),(0,n.jsxs)(h.A,{flexGrow:1,children:[(0,n.jsx)(Y.A,{variant:"h6",children:e.name}),e.teacherName&&(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:["Teacher: ",e.teacherName]}),e.schoolYear&&(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:["Year: ",e.schoolYear]})]})]}),(0,n.jsxs)(h.A,{display:"flex",justifyContent:"flex-end",children:[(0,n.jsx)(K.A,{onClick:()=>{o(e),u({name:e.name,teacherName:e.teacherName||"",schoolYear:e.schoolYear||""}),i(!0)},children:(0,n.jsx)(eK.A,{})}),(0,n.jsx)(K.A,{color:"error",onClick:()=>m(e.id),children:(0,n.jsx)(eV.A,{})})]})]})})},e.id))}),0===s.length&&(0,n.jsx)(eg.A,{sx:{textAlign:"center",py:8},children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(te.A,{sx:{fontSize:64,color:"text.secondary",mb:2}}),(0,n.jsx)(Y.A,{variant:"h6",color:"textSecondary",gutterBottom:!0,children:"No classes found"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"Start by adding your first class"})]})}),(0,n.jsxs)(eH.A,{open:a,onClose:j,maxWidth:"sm",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:l?"Edit Class":"Add New Class"}),(0,n.jsx)(e_.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,sx:{mt:1},children:[(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Class Name",value:x.name,onChange:e=>u({...x,name:e.target.value}),required:!0})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Teacher Name",value:x.teacherName,onChange:e=>u({...x,teacherName:e.target.value})})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"School Year",value:x.schoolYear,onChange:e=>u({...x,schoolYear:e.target.value}),placeholder:"e.g., Year 1, Year 2, etc."})})]})}),(0,n.jsxs)(eU.A,{children:[(0,n.jsx)(ek.A,{onClick:j,children:"Cancel"}),(0,n.jsx)(ek.A,{onClick:A,variant:"contained",disabled:!x.name.trim(),children:l?"Update":"Add Class"})]})]}),(0,n.jsx)(eJ.A,{open:d.open,autoHideDuration:4e3,onClose:g,children:(0,n.jsx)(eq.A,{onClose:g,severity:d.severity,children:d.message})})]})},ts=function(){let{state:e,api:t}=_(),{genres:s}=e,[a,i]=(0,r.useState)(!1),[l,o]=(0,r.useState)(null),[d,c]=(0,r.useState)({open:!1,message:"",severity:"success"}),[x,u]=(0,r.useState)({name:"",description:""}),m=async e=>{if(window.confirm("Are you sure you want to delete this genre?"))try{await t.deleteGenre(e),c({open:!0,message:"Genre deleted successfully",severity:"success"})}catch(e){c({open:!0,message:"Failed to delete genre",severity:"error"})}},A=async()=>{try{l?(await t.updateGenre(l.id,x),c({open:!0,message:"Genre updated successfully",severity:"success"})):(await t.createGenre(x),c({open:!0,message:"Genre created successfully",severity:"success"})),i(!1)}catch(e){c({open:!0,message:"Failed to save genre",severity:"error"})}},j=()=>{i(!1),o(null)},g=()=>{c({...d,open:!1})};return(0,n.jsxs)(h.A,{children:[(0,n.jsxs)(h.A,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,n.jsx)(Y.A,{variant:"h4",children:"Genres"}),(0,n.jsx)(ek.A,{variant:"contained",startIcon:(0,n.jsx)(em.A,{}),onClick:()=>{o(null),u({name:"",description:""}),i(!0)},children:"Add Genre"})]}),(0,n.jsx)(ev.A,{container:!0,spacing:3,children:s.map(e=>(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,lg:4,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsxs)(h.A,{display:"flex",alignItems:"center",mb:2,children:[(0,n.jsx)(ec.A,{sx:{fontSize:40,color:"primary.main",mr:2}}),(0,n.jsxs)(h.A,{flexGrow:1,children:[(0,n.jsx)(Y.A,{variant:"h6",children:e.name}),e.description&&(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:e.description})]})]}),(0,n.jsxs)(h.A,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[(0,n.jsx)(ef.A,{label:e.isPredefined?"Predefined":"Custom",size:"small",color:e.isPredefined?"primary":"default",variant:"outlined"}),(0,n.jsxs)(h.A,{children:[(0,n.jsx)(K.A,{onClick:()=>{o(e),u({name:e.name,description:e.description||""}),i(!0)},disabled:e.isPredefined,children:(0,n.jsx)(eK.A,{})}),(0,n.jsx)(K.A,{color:"error",onClick:()=>m(e.id),disabled:e.isPredefined,children:(0,n.jsx)(eV.A,{})})]})]})]})})},e.id))}),0===s.length&&(0,n.jsx)(eg.A,{sx:{textAlign:"center",py:8},children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(ec.A,{sx:{fontSize:64,color:"text.secondary",mb:2}}),(0,n.jsx)(Y.A,{variant:"h6",color:"textSecondary",gutterBottom:!0,children:"No genres found"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"Start by adding your first genre"})]})}),(0,n.jsxs)(eH.A,{open:a,onClose:j,maxWidth:"sm",fullWidth:!0,children:[(0,n.jsx)(e$.A,{children:l?"Edit Genre":"Add New Genre"}),(0,n.jsx)(e_.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,sx:{mt:1},children:[(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Genre Name",value:x.name,onChange:e=>u({...x,name:e.target.value}),required:!0})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,multiline:!0,rows:3,label:"Description (optional)",value:x.description,onChange:e=>u({...x,description:e.target.value})})})]})}),(0,n.jsxs)(eU.A,{children:[(0,n.jsx)(ek.A,{onClick:j,children:"Cancel"}),(0,n.jsx)(ek.A,{onClick:A,variant:"contained",disabled:!x.name.trim(),children:l?"Update":"Add Genre"})]})]}),(0,n.jsx)(eJ.A,{open:d.open,autoHideDuration:4e3,onClose:g,children:(0,n.jsx)(eq.A,{onClose:g,severity:d.severity,children:d.message})})]})};var tn=s(734),tr=s(4336),ta=s(8725);let ti=function(){var e;let{state:t,api:s}=_(),{students:a,recommendations:i}=t,[l,o]=(0,r.useState)(""),[d,c]=(0,r.useState)(!1),[x,u]=(0,r.useState)([]),[m,A]=(0,r.useState)({open:!1,message:"",severity:"success"}),j=async()=>{if(l){c(!0);try{let e=await s.getRecommendations(l,5);u(e),A({open:!0,message:"Recommendations generated successfully",severity:"success"})}catch(e){A({open:!0,message:"Failed to generate recommendations",severity:"error"})}finally{c(!1)}}},g=()=>{A({...m,open:!1})},y=a.find(e=>e.id===l);return(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{variant:"h4",gutterBottom:!0,children:"Book Recommendations"}),(0,n.jsx)(eg.A,{sx:{mb:3},children:(0,n.jsx)(ey.A,{children:(0,n.jsxs)(ev.A,{container:!0,spacing:2,alignItems:"center",children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsxs)(eT.A,{fullWidth:!0,children:[(0,n.jsx)(eR.A,{children:"Select Student"}),(0,n.jsxs)(ez.A,{value:l,onChange:e=>o(e.target.value),label:"Select Student",sx:{fontSize:"1.4rem",minHeight:64,height:64,"& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiMenuItem-root":{fontSize:"1.4rem",minHeight:56,padding:"12px 16px"},"& .MuiSelect-icon":{fontSize:"2rem"}},children:[(0,n.jsx)(eB.A,{value:"",children:"Choose a student..."}),a.map(e=>(0,n.jsx)(eB.A,{value:e.id,children:e.name},e.id))]})]})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(ek.A,{variant:"contained",fullWidth:!0,onClick:j,disabled:!l||d,startIcon:d?(0,n.jsx)(tn.A,{size:20}):(0,n.jsx)(ta.A,{}),children:d?"Generating...":"Generate Recommendations"})})]})})}),y&&(0,n.jsx)(eg.A,{sx:{mb:3},children:(0,n.jsxs)(ey.A,{children:[(0,n.jsxs)(Y.A,{variant:"h6",gutterBottom:!0,children:["Selected Student: ",y.name]}),(0,n.jsxs)(h.A,{display:"flex",gap:1,flexWrap:"wrap",children:[y.readingLevel&&(0,n.jsx)(ef.A,{label:`Level: ${y.readingLevel}`,color:"primary",variant:"outlined"}),(0,n.jsx)(ef.A,{label:`${(null==(e=y.readingSessions)?void 0:e.length)||0} sessions logged`,variant:"outlined"})]})]})}),x.length>0&&(0,n.jsxs)(ev.A,{container:!0,spacing:3,children:[(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(Y.A,{variant:"h5",gutterBottom:!0,children:"Recommended Books"})}),x.map((e,s)=>(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eg.A,{children:(0,n.jsx)(ey.A,{children:(0,n.jsxs)(h.A,{display:"flex",alignItems:"flex-start",children:[(0,n.jsx)(eo.A,{sx:{fontSize:40,color:"primary.main",mr:2,mt:1}}),(0,n.jsxs)(h.A,{flexGrow:1,children:[(0,n.jsx)(Y.A,{variant:"h6",gutterBottom:!0,children:e.book.title}),(0,n.jsxs)(Y.A,{variant:"subtitle1",color:"textSecondary",gutterBottom:!0,children:["by ",e.book.author]}),e.book.genreIds&&(0,n.jsx)(h.A,{mb:2,children:e.book.genreIds.map(e=>{let s=t.genres.find(t=>t.id===e);return s?(0,n.jsx)(ef.A,{label:s.name,size:"small",variant:"outlined",sx:{mr:.5,mb:.5}},e):null})}),(0,n.jsxs)(h.A,{mb:2,children:[(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:"Recommendation Score:"}),(0,n.jsx)(tr.A,{value:Math.min(5,Math.max(1,Math.round(e.score))),readOnly:!0,size:"small"})]}),e.reasoning&&e.reasoning.length>0&&(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:"Why recommended:"}),(0,n.jsx)("ul",{style:{margin:0,paddingLeft:"1.2rem"},children:e.reasoning.map((e,t)=>(0,n.jsx)("li",{children:(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:e})},t))})]})]})]})})})},s))]}),l&&!d&&0===x.length&&(0,n.jsx)(eg.A,{sx:{textAlign:"center",py:8},children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(ed.A,{sx:{fontSize:64,color:"text.secondary",mb:2}}),(0,n.jsx)(Y.A,{variant:"h6",color:"textSecondary",gutterBottom:!0,children:"No recommendations yet"}),(0,n.jsxs)(Y.A,{variant:"body2",color:"textSecondary",children:['Click "Generate Recommendations" to get personalized book suggestions for ',null==y?void 0:y.name]})]})}),(0,n.jsx)(eJ.A,{open:m.open,autoHideDuration:4e3,onClose:g,children:(0,n.jsx)(eq.A,{onClose:g,severity:m.severity,children:m.message})})]})};var tl=s(4845);let to=function(){let{state:e,api:t}=_(),{settings:s}=e,[a,i]=(0,r.useState)({recentlyReadDays:7,needsAttentionDays:14}),[l,o]=(0,r.useState)(!1),[d,c]=(0,r.useState)({open:!1,message:"",severity:"success"});(0,r.useEffect)(()=>{(null==s?void 0:s.readingStatusSettings)&&i({recentlyReadDays:s.readingStatusSettings.recentlyReadDays,needsAttentionDays:s.readingStatusSettings.needsAttentionDays})},[s]);let x=async()=>{o(!0);try{await t.updateSettings({readingStatusSettings:a}),c({open:!0,message:"Settings saved successfully",severity:"success"})}catch(e){c({open:!0,message:"Failed to save settings",severity:"error"})}finally{o(!1)}},u=()=>{c({...d,open:!1})},m=e=>t=>{let s=parseInt(t.target.value);i(t=>({...t,[e]:isNaN(s)?0:s}))};return(0,n.jsxs)(h.A,{children:[(0,n.jsx)(Y.A,{variant:"h4",gutterBottom:!0,children:"Settings"}),(0,n.jsxs)(ev.A,{container:!0,spacing:3,children:[(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(Y.A,{variant:"h6",gutterBottom:!0,children:"Reading Status Configuration"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",paragraph:!0,children:"Configure how the system determines reading status and attention requirements."}),(0,n.jsxs)(ev.A,{container:!0,spacing:3,children:[(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Recently Read (days)",type:"number",value:a.recentlyReadDays,onChange:m("recentlyReadDays"),helperText:"Students who have read within this many days are considered 'recently active'",inputProps:{min:1,max:365}})}),(0,n.jsx)(ev.A,{item:!0,xs:12,children:(0,n.jsx)(eL.A,{fullWidth:!0,label:"Needs Attention (days)",type:"number",value:a.needsAttentionDays,onChange:m("needsAttentionDays"),helperText:"Students who haven't read within this many days need attention",inputProps:{min:1,max:365}})})]}),a.recentlyReadDays>=a.needsAttentionDays&&(0,n.jsx)(eq.A,{severity:"warning",sx:{mt:2},children:"Recently Read days should be less than Needs Attention days"}),(0,n.jsx)(h.A,{mt:3,children:(0,n.jsx)(ek.A,{variant:"contained",startIcon:(0,n.jsx)(tl.A,{}),onClick:x,disabled:l||a.recentlyReadDays>=a.needsAttentionDays,children:l?"Saving...":"Save Settings"})})]})})}),(0,n.jsx)(ev.A,{item:!0,xs:12,md:6,children:(0,n.jsx)(eg.A,{children:(0,n.jsxs)(ey.A,{children:[(0,n.jsx)(Y.A,{variant:"h6",gutterBottom:!0,children:"About Reading Assistant"}),(0,n.jsx)(Y.A,{variant:"body2",paragraph:!0,children:"A comprehensive reading tracking system designed for primary school teachers and volunteers to monitor student reading progress and provide personalized book recommendations."}),(0,n.jsx)(V.A,{sx:{my:2}}),(0,n.jsx)(Y.A,{variant:"subtitle2",gutterBottom:!0,children:"Features:"}),(0,n.jsxs)("ul",{style:{margin:0,paddingLeft:"1.2rem"},children:[(0,n.jsx)("li",{children:(0,n.jsx)(Y.A,{variant:"body2",children:"Track reading sessions with detailed assessments"})}),(0,n.jsx)("li",{children:(0,n.jsx)(Y.A,{variant:"body2",children:"Manage student profiles and reading preferences"})}),(0,n.jsx)("li",{children:(0,n.jsx)(Y.A,{variant:"body2",children:"AI-powered book recommendations"})}),(0,n.jsx)("li",{children:(0,n.jsx)(Y.A,{variant:"body2",children:"Monitor reading progress and identify students needing attention"})}),(0,n.jsx)("li",{children:(0,n.jsx)(Y.A,{variant:"body2",children:"Comprehensive book and genre management"})})]}),(0,n.jsx)(V.A,{sx:{my:2}}),(0,n.jsx)(Y.A,{variant:"subtitle2",gutterBottom:!0,children:"Data Storage:"}),(0,n.jsx)(Y.A,{variant:"body2",color:"textSecondary",children:"Built with Cloudflare Workers and KV storage for fast, reliable data access."})]})})})]}),(0,n.jsx)(eJ.A,{open:d.open,autoHideDuration:4e3,onClose:u,children:(0,n.jsx)(eq.A,{onClose:u,severity:d.severity,children:d.message})})]})};console.log("App.jsx loaded, defining routes...");let td=(0,o.A)({palette:{primary:{main:"#1976d2",light:"#42a5f5",dark:"#1565c0"},secondary:{main:"#dc004e",light:"#ff5983",dark:"#9a0036"},background:{default:"#f5f5f5",paper:"#ffffff"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},shape:{borderRadius:8},components:{MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)",transition:"box-shadow 0.3s ease-in-out","&:hover":{boxShadow:"0 4px 16px rgba(0,0,0,0.15)"}}}},MuiButton:{styleOverrides:{root:{textTransform:"none",borderRadius:8}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}},MuiSelect:{styleOverrides:{root:{fontSize:"1.4rem",minHeight:"64px","& .MuiSelect-select":{padding:"20px 16px",fontSize:"1.4rem",minHeight:"24px"},"& .MuiInputLabel-root":{fontSize:"1.2rem"},"& .MuiSelect-icon":{fontSize:"2rem"}}}},MuiMenuItem:{styleOverrides:{root:{fontSize:"1.4rem",minHeight:"56px",padding:"12px 16px"}}},MuiIconButton:{styleOverrides:{root:{fontSize:"1.5rem",width:"48px",height:"48px","& .MuiSvgIcon-root":{fontSize:"1.8rem"}}}}}}),tc=document.getElementById("root");(0,a.createRoot)(tc).render((0,n.jsx)(r.StrictMode,{children:(0,n.jsx)(function(){return(0,n.jsxs)(d.A,{theme:td,children:[(0,n.jsx)(c.Ay,{}),(0,n.jsx)($,{children:(0,n.jsx)(i.Kd,{children:(0,n.jsx)(h.A,{sx:{display:"flex",minHeight:"100vh"},children:(0,n.jsx)(ej,{children:(0,n.jsxs)(l.BV,{children:[(0,n.jsx)(l.qh,{path:"/",element:(0,n.jsx)(eE,{})}),(0,n.jsx)(l.qh,{path:"/students/:id",element:(0,n.jsx)(e0,{})}),(0,n.jsx)(l.qh,{path:"/students",element:(0,n.jsx)(eZ,{})}),(0,n.jsx)(l.qh,{path:"/sessions",element:(0,n.jsx)(e7,{})}),(0,n.jsx)(l.qh,{path:"/books",element:(0,n.jsxs)(n.Fragment,{children:[console.log("Rendering BooksPage route")||null,(0,n.jsx)(e9,{})]})}),(0,n.jsx)(l.qh,{path:"/classes",element:(0,n.jsx)(tt,{})}),(0,n.jsx)(l.qh,{path:"/genres",element:(0,n.jsx)(ts,{})}),(0,n.jsx)(l.qh,{path:"/recommendations",element:(0,n.jsx)(ti,{})}),(0,n.jsx)(l.qh,{path:"/settings",element:(0,n.jsx)(to,{})})]})})})})})]})},{})}))}},l={};function o(e){var t=l[e];if(void 0!==t)return t.exports;var s=l[e]={exports:{}};return i[e](s,s.exports,o),s.exports}o.m=i,o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,o.t=function(s,n){if(1&n&&(s=this(s)),8&n||"object"==typeof s&&s&&(4&n&&s.__esModule||16&n&&"function"==typeof s.then))return s;var r=Object.create(null);o.r(r);var a={};e=e||[null,t({}),t([]),t(t)];for(var i=2&n&&s;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach(e=>{a[e]=()=>s[e]});return a.default=()=>s,o.d(r,a),r},o.d=(e,t)=>{for(var s in t)o.o(t,s)&&!o.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s=[],o.O=(e,t,n,r)=>{if(t){r=r||0;for(var a=s.length;a>0&&s[a-1][2]>r;a--)s[a]=s[a-1];s[a]=[t,n,r];return}for(var i=1/0,a=0;a<s.length;a++){for(var[t,n,r]=s[a],l=!0,d=0;d<t.length;d++)(!1&r||i>=r)&&Object.keys(o.O).every(e=>o.O[e](t[d]))?t.splice(d--,1):(l=!1,r<i&&(i=r));if(l){s.splice(a--,1);var c=n();void 0!==c&&(e=c)}}return e},n={410:0},o.O.j=e=>0===n[e],r=(e,t)=>{var s,r,[a,i,l]=t,d=0;if(a.some(e=>0!==n[e])){for(s in i)o.o(i,s)&&(o.m[s]=i[s]);if(l)var c=l(o)}for(e&&e(t);d<a.length;d++)r=a[d],o.o(n,r)&&n[r]&&n[r][0](),n[r]=0;return o.O(c)},(a=self.webpackChunkreading_assistant=self.webpackChunkreading_assistant||[]).forEach(r.bind(null,0)),a.push=r.bind(null,a.push.bind(a));var d=o.O(void 0,["783","535","556"],function(){return o(3940)});d=o.O(d)})();