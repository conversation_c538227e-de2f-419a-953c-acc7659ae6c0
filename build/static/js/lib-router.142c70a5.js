/*! For license information please see lib-router.142c70a5.js.LICENSE.txt */
"use strict";(self.webpackChunkreading_assistant=self.webpackChunkreading_assistant||[]).push([["535"],{5588:function(e,t,r){var n,a,o,i;function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}r.d(t,{Gh:()=>S,HS:()=>C,Oi:()=>c,Rr:()=>f,pX:()=>k,pb:()=>E,rc:()=>n,tH:()=>U,ue:()=>m,yD:()=>R,zR:()=>u}),(o=n||(n={})).Pop="POP",o.Push="PUSH",o.Replace="REPLACE";let s="popstate";function u(e){return void 0===e&&(e={}),function(e,t,r,a){void 0===a&&(a={});let{window:o=document.defaultView,v5Compat:i=!1}=a,u=o.history,h=n.Pop,f=null,m=g();function g(){return(u.state||{idx:null}).idx}function y(){h=n.Pop;let e=g(),t=null==e?null:e-m;m=e,f&&f({action:h,location:E.location,delta:t})}function b(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,r="string"==typeof e?e:v(e);return c(t,"No window.location.(origin|href) available to create URL for href: "+(r=r.replace(/ $/,"%20"))),new URL(r,t)}null==m&&(m=0,u.replaceState(l({},u.state,{idx:m}),""));let E={get action(){return h},get location(){return e(o,u)},listen(e){if(f)throw Error("A history only accepts one active listener");return o.addEventListener(s,y),f=e,()=>{o.removeEventListener(s,y),f=null}},createHref:e=>t(o,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){h=n.Push;let r=d(E.location,e,t);let a=p(r,m=g()+1),l=E.createHref(r);try{u.pushState(a,"",l)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;o.location.assign(l)}i&&f&&f({action:h,location:E.location,delta:1})},replace:function(e,t){h=n.Replace;let a=d(E.location,e,t);r&&r(a,e);let o=p(a,m=g()),l=E.createHref(a);u.replaceState(o,"",l),i&&f&&f({action:h,location:E.location,delta:0})},go:e=>u.go(e)};return E}(function(e,t){let{pathname:r,search:n,hash:a}=e.location;return d("",{pathname:r,search:n,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:v(t)},null,e)}function c(e,t){if(!1===e||null==e)throw Error(t)}function h(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw Error(t)}catch(e){}}}function p(e,t){return{usr:e.state,key:e.key,idx:t}}function d(e,t,r,n){return void 0===r&&(r=null),l({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?f(t):t,{state:r,key:t&&t.key||n||Math.random().toString(36).substr(2,8)})}function v(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function f(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function m(e,t,r){return void 0===r&&(r="/"),function(e,t,r,n){let a=E(("string"==typeof t?f(t):t).pathname||"/",r);if(null==a)return null;let o=function e(t,r,n,a){void 0===r&&(r=[]),void 0===n&&(n=[]),void 0===a&&(a="");let o=(t,o,i)=>{var l,s;let u,h,p={relativePath:void 0===i?t.path||"":i,caseSensitive:!0===t.caseSensitive,childrenIndex:o,route:t};p.relativePath.startsWith("/")&&(c(p.relativePath.startsWith(a),'Absolute route path "'+p.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),p.relativePath=p.relativePath.slice(a.length));let d=C([a,p.relativePath]),v=n.concat(p);t.children&&t.children.length>0&&(c(!0!==t.index,'Index routes must not have child routes. Please remove all child routes from route path "'+d+'".'),e(t.children,r,v,d)),(null!=t.path||t.index)&&r.push({path:d,score:(l=d,s=t.index,h=(u=l.split("/")).length,u.some(y)&&(h+=-2),s&&(h+=2),u.filter(e=>!y(e)).reduce((e,t)=>e+(g.test(t)?3:""===t?1:10),h)),routesMeta:v})};return t.forEach((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let r of function e(t){let r=t.split("/");if(0===r.length)return[];let[n,...a]=r,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(0===a.length)return o?[i,""]:[i];let l=e(a.join("/")),s=[];return s.push(...l.map(e=>""===e?i:[i,e].join("/"))),o&&s.push(...l),s.map(e=>t.startsWith("/")&&""===e?"/":e)}(e.path))o(e,t,r);else o(e,t)}),r}(e);o.sort((e,t)=>{var r,n;return e.score!==t.score?t.score-e.score:(r=e.routesMeta.map(e=>e.childrenIndex),n=t.routesMeta.map(e=>e.childrenIndex),r.length===n.length&&r.slice(0,-1).every((e,t)=>e===n[t])?r[r.length-1]-n[n.length-1]:0)});let i=null;for(let e=0;null==i&&e<o.length;++e){let t=function(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return h(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}(a);i=function(e,t,r){void 0===r&&(r=!1);let{routesMeta:n}=e,a={},o="/",i=[];for(let e=0;e<n.length;++e){let l=n[e],s=e===n.length-1,u="/"===o?t:t.slice(o.length)||"/",c=b({path:l.relativePath,caseSensitive:l.caseSensitive,end:s},u),h=l.route;if(!c&&s&&r&&!n[n.length-1].route.index&&(c=b({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:C([o,c.pathname]),pathnameBase:w(C([o,c.pathnameBase])),route:h}),"/"!==c.pathnameBase&&(o=C([o,c.pathnameBase]))}return i}(o[e],t,n)}return i}(e,t,r,!1)}(i=a||(a={})).data="data",i.deferred="deferred",i.redirect="redirect",i.error="error";let g=/^:[\w-]+$/,y=e=>"*"===e;function b(e,t){var r,n,a;let o,i;"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[l,s]=(r=e.path,n=e.caseSensitive,a=e.end,void 0===n&&(n=!1),void 0===a&&(a=!0),h("*"===r||!r.endsWith("*")||r.endsWith("/*"),'Route path "'+r+'" will be treated as if it were "'+r.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+r.replace(/\*$/,"/*")+'".'),o=[],i="^"+r.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,r)=>(o.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")),r.endsWith("*")?(o.push({paramName:"*"}),i+="*"===r||"/*"===r?"(.*)$":"(?:\\/(.+)|\\/*)$"):a?i+="\\/*$":""!==r&&"/"!==r&&(i+="(?:(?=\\/|$))"),[new RegExp(i,n?void 0:"i"),o]),u=t.match(l);if(!u)return null;let c=u[0],p=c.replace(/(.)\/+$/,"$1"),d=u.slice(1);return{params:s.reduce((e,t,r)=>{let{paramName:n,isOptional:a}=t;if("*"===n){let e=d[r]||"";p=c.slice(0,c.length-e.length).replace(/(.)\/+$/,"$1")}let o=d[r];return a&&!o?e[n]=void 0:e[n]=(o||"").replace(/%2F/g,"/"),e},{}),pathname:c,pathnameBase:p,pattern:e}}function E(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function x(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field [")+JSON.stringify(n)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function R(e,t){let r=e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0);return t?r.map((e,t)=>t===r.length-1?e.pathname:e.pathnameBase):r.map(e=>e.pathnameBase)}function S(e,t,r,n){let a,o;void 0===n&&(n=!1),"string"==typeof e?a=f(e):(c(!(a=l({},e)).pathname||!a.pathname.includes("?"),x("?","pathname","search",a)),c(!a.pathname||!a.pathname.includes("#"),x("#","pathname","hash",a)),c(!a.search||!a.search.includes("#"),x("#","search","hash",a)));let i=""===e||""===a.pathname,s=i?"/":a.pathname;if(null==s)o=r;else{let e=t.length-1;if(!n&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let u=function(e,t){var r;let n;void 0===t&&(t="/");let{pathname:a,search:o="",hash:i=""}="string"==typeof e?f(e):e;return{pathname:a?a.startsWith("/")?a:(r=a,n=t.replace(/\/+$/,"").split("/"),r.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"):t,search:P(o),hash:O(i)}}(a,o),h=s&&"/"!==s&&s.endsWith("/"),p=(i||"."===s)&&r.endsWith("/");return!u.pathname.endsWith("/")&&(h||p)&&(u.pathname+="/"),u}let C=e=>e.join("/").replace(/\/\/+/g,"/"),w=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),P=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",O=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class U extends Error{}function k(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}Symbol("deferred")},4976:function(e,t,r){r.d(t,{Kd:()=>v});var n,a,o,i,l,s,u=r(6540),c=r(961),h=r(7767),p=r(5588);try{window.__reactRouterVersion="6"}catch(e){}let d=(o||(o=r.t(u,2))).startTransition;function v(e){let{basename:t,children:r,future:n,window:a}=e,o=u.useRef();null==o.current&&(o.current=(0,p.zR)({window:a,v5Compat:!0}));let i=o.current,[l,s]=u.useState({action:i.action,location:i.location}),{v7_startTransition:c}=n||{},v=u.useCallback(e=>{c&&d?d(()=>s(e)):s(e)},[s,c]);return u.useLayoutEffect(()=>i.listen(v),[i,v]),u.useEffect(()=>(0,h.V8)(n),[n]),u.createElement(h.Ix,{basename:t,children:r,location:l.location,navigationType:l.action,navigator:i,future:n})}(i||(i=r.t(c,2))).flushSync,(o||(o=r.t(u,2))).useId,"undefined"!=typeof window&&void 0!==window.document&&window.document.createElement,(n=l||(l={})).UseScrollRestoration="useScrollRestoration",n.UseSubmit="useSubmit",n.UseSubmitFetcher="useSubmitFetcher",n.UseFetcher="useFetcher",n.useViewTransitionState="useViewTransitionState",(a=s||(s={})).UseFetcher="useFetcher",a.UseFetchers="useFetchers",a.UseScrollRestoration="useScrollRestoration"},7767:function(e,t,r){r.d(t,{BV:()=>L,Ix:()=>B,V8:()=>k,Zp:()=>b,g:()=>E,qh:()=>_,zy:()=>g});var n,a,o,i,l=r(6540),s=r(5588);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}let c=l.createContext(null),h=l.createContext(null),p=l.createContext(null),d=l.createContext(null),v=l.createContext({outlet:null,matches:[],isDataRoute:!1}),f=l.createContext(null);function m(){return null!=l.useContext(d)}function g(){return m()||(0,s.Oi)(!1),l.useContext(d).location}function y(e){l.useContext(p).static||l.useLayoutEffect(e)}function b(){let{isDataRoute:e}=l.useContext(v);return e?function(){let e,{router:t}=(C.UseNavigateStable,(e=l.useContext(c))||(0,s.Oi)(!1),e),r=P(w.UseNavigateStable),n=l.useRef(!1);return y(()=>{n.current=!0}),l.useCallback(function(e,a){void 0===a&&(a={}),n.current&&("number"==typeof e?t.navigate(e):t.navigate(e,u({fromRouteId:r},a)))},[t,r])}():function(){m()||(0,s.Oi)(!1);let e=l.useContext(c),{basename:t,future:r,navigator:n}=l.useContext(p),{matches:a}=l.useContext(v),{pathname:o}=g(),i=JSON.stringify((0,s.yD)(a,r.v7_relativeSplatPath)),u=l.useRef(!1);return y(()=>{u.current=!0}),l.useCallback(function(r,a){if(void 0===a&&(a={}),!u.current)return;if("number"==typeof r)return void n.go(r);let l=(0,s.Gh)(r,JSON.parse(i),o,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:(0,s.HS)([t,l.pathname])),(a.replace?n.replace:n.push)(l,a.state,a)},[t,n,i,o,e])}()}function E(){let{matches:e}=l.useContext(v),t=e[e.length-1];return t?t.params:{}}let x=l.createElement(function(){var e;let t,r,n,a,o=(r=l.useContext(f),n=(w.UseRouteError,(t=l.useContext(h))||(0,s.Oi)(!1),t),a=P(w.UseRouteError),void 0!==r?r:null==(e=n.errors)?void 0:e[a]),i=(0,s.pX)(o)?o.status+" "+o.statusText:o instanceof Error?o.message:JSON.stringify(o),u=o instanceof Error?o.stack:null;return l.createElement(l.Fragment,null,l.createElement("h2",null,"Unexpected Application Error!"),l.createElement("h3",{style:{fontStyle:"italic"}},i),u?l.createElement("pre",{style:{padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"}},u):null,null)},null);class R extends l.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?l.createElement(v.Provider,{value:this.props.routeContext},l.createElement(f.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function S(e){let{routeContext:t,match:r,children:n}=e,a=l.useContext(c);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),l.createElement(v.Provider,{value:t},n)}var C=((n=C||{}).UseBlocker="useBlocker",n.UseRevalidator="useRevalidator",n.UseNavigateStable="useNavigate",n),w=((a=w||{}).UseBlocker="useBlocker",a.UseLoaderData="useLoaderData",a.UseActionData="useActionData",a.UseRouteError="useRouteError",a.UseNavigation="useNavigation",a.UseRouteLoaderData="useRouteLoaderData",a.UseMatches="useMatches",a.UseRevalidator="useRevalidator",a.UseNavigateStable="useNavigate",a.UseRouteId="useRouteId",a);function P(e){let t,r=((t=l.useContext(v))||(0,s.Oi)(!1),t),n=r.matches[r.matches.length-1];return n.route.id||(0,s.Oi)(!1),n.route.id}let O={},U=(e,t,r)=>{};function k(e,t){(null==e?void 0:e.v7_startTransition)===void 0&&U("v7_startTransition","React Router will begin wrapping state updates in `React.startTransition` in v7","https://reactrouter.com/v6/upgrading/future#v7_starttransition"),(null==e?void 0:e.v7_relativeSplatPath)!==void 0||t&&void 0!==t.v7_relativeSplatPath||U("v7_relativeSplatPath","Relative route resolution within Splat routes is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath"),t&&(void 0===t.v7_fetcherPersist&&U("v7_fetcherPersist","The persistence behavior of fetchers is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist"),void 0===t.v7_normalizeFormMethod&&U("v7_normalizeFormMethod","Casing of `formMethod` fields is being normalized to uppercase in v7","https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod"),void 0===t.v7_partialHydration&&U("v7_partialHydration","`RouterProvider` hydration behavior is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_partialhydration"),void 0===t.v7_skipActionErrorRevalidation&&U("v7_skipActionErrorRevalidation","The revalidation behavior after 4xx/5xx `action` responses is changing in v7","https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation"))}function _(e){(0,s.Oi)(!1)}function B(e){let{basename:t="/",children:r=null,location:n,navigationType:a=s.rc.Pop,navigator:o,static:i=!1,future:c}=e;m()&&(0,s.Oi)(!1);let h=t.replace(/^\/*/,"/"),v=l.useMemo(()=>({basename:h,navigator:o,static:i,future:u({v7_relativeSplatPath:!1},c)}),[h,c,o,i]);"string"==typeof n&&(n=(0,s.Rr)(n));let{pathname:f="/",search:g="",hash:y="",state:b=null,key:E="default"}=n,x=l.useMemo(()=>{let e=(0,s.pb)(f,h);return null==e?null:{location:{pathname:e,search:g,hash:y,state:b,key:E},navigationType:a}},[h,f,g,y,b,E,a]);return null==x?null:l.createElement(p.Provider,{value:v},l.createElement(d.Provider,{children:r,value:x}))}function L(e){let{children:t,location:r}=e;return function(e,t,r,n){let a;m()||(0,s.Oi)(!1);let{navigator:o}=l.useContext(p),{matches:i}=l.useContext(v),c=i[i.length-1],h=c?c.params:{};c&&c.pathname;let f=c?c.pathnameBase:"/";c&&c.route;let y=g();if(t){var b;let e="string"==typeof t?(0,s.Rr)(t):t;"/"===f||(null==(b=e.pathname)?void 0:b.startsWith(f))||(0,s.Oi)(!1),a=e}else a=y;let E=a.pathname||"/",C=E;if("/"!==f){let e=f.replace(/^\//,"").split("/");C="/"+E.replace(/^\//,"").split("/").slice(e.length).join("/")}let w=(0,s.ue)(e,{pathname:C}),P=function(e,t,r,n){var a,o;if(void 0===t&&(t=[]),void 0===r&&(r=null),void 0===n&&(n=null),null==e){if(!r)return null;if(r.errors)e=r.matches;else{if(null==(o=n)||!o.v7_partialHydration||0!==t.length||r.initialized||!(r.matches.length>0))return null;e=r.matches}}let i=e,u=null==(a=r)?void 0:a.errors;if(null!=u){let e=i.findIndex(e=>e.route.id&&(null==u?void 0:u[e.route.id])!==void 0);e>=0||(0,s.Oi)(!1),i=i.slice(0,Math.min(i.length,e+1))}let c=!1,h=-1;if(r&&n&&n.v7_partialHydration)for(let e=0;e<i.length;e++){let t=i[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(h=e),t.route.id){let{loaderData:e,errors:n}=r,a=t.route.loader&&void 0===e[t.route.id]&&(!n||void 0===n[t.route.id]);if(t.route.lazy||a){c=!0,i=h>=0?i.slice(0,h+1):[i[0]];break}}}return i.reduceRight((e,n,a)=>{var o;let s,p=!1,d=null,v=null;r&&(s=u&&n.route.id?u[n.route.id]:void 0,d=n.route.errorElement||x,c&&(h<0&&0===a?(o="route-fallback",O[o]||(O[o]=!0),p=!0,v=null):h===a&&(p=!0,v=n.route.hydrateFallbackElement||null)));let f=t.concat(i.slice(0,a+1)),m=()=>{let t;return t=s?d:p?v:n.route.Component?l.createElement(n.route.Component,null):n.route.element?n.route.element:e,l.createElement(S,{match:n,routeContext:{outlet:e,matches:f,isDataRoute:null!=r},children:t})};return r&&(n.route.ErrorBoundary||n.route.errorElement||0===a)?l.createElement(R,{location:r.location,revalidation:r.revalidation,component:d,error:s,children:m(),routeContext:{outlet:null,matches:f,isDataRoute:!0}}):m()},null)}(w&&w.map(e=>Object.assign({},e,{params:Object.assign({},h,e.params),pathname:(0,s.HS)([f,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?f:(0,s.HS)([f,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),i,void 0,void 0);return t&&P?l.createElement(d.Provider,{value:{location:u({pathname:"/",search:"",hash:"",state:null,key:"default"},a),navigationType:s.rc.Pop}},P):P}(function e(t,r){void 0===r&&(r=[]);let n=[];return l.Children.forEach(t,(t,a)=>{if(!l.isValidElement(t))return;let o=[...r,a];if(t.type===l.Fragment)return void n.push.apply(n,e(t.props.children,o));t.type!==_&&(0,s.Oi)(!1),t.props.index&&t.props.children&&(0,s.Oi)(!1);let i={id:t.props.id||o.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,Component:t.props.Component,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,errorElement:t.props.errorElement,ErrorBoundary:t.props.ErrorBoundary,hasErrorBoundary:null!=t.props.ErrorBoundary||null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle,lazy:t.props.lazy};t.props.children&&(i.children=e(t.props.children,o)),n.push(i)}),n}(t),r)}(i||(i=r.t(l,2))).startTransition;var F=((o=F||{})[o.pending=0]="pending",o[o.success=1]="success",o[o.error=2]="error",o);new Promise(()=>{}),l.Component}}]);