{"name": "reading-assistant", "version": "1.6.2", "description": "A comprehensive reading tracking system for primary school students with AI-powered book recommendations", "main": "src/worker.js", "scripts": {"dev": "wrangler dev", "start": "wrangler dev --port 3000", "dev:frontend": "rsbuild dev", "build": "rsbuild build", "deploy": "wrangler deploy", "cf:login": "wrangler auth login", "cf:preview": "wrangler dev --local", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write src"}, "keywords": ["reading", "education", "primary-school", "cloudflare", "react", "ai-recommendations"], "author": "Reading Manager Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.27.3", "@cloudflare/kv-asset-handler": "^0.3.4", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.3", "@mui/material": "^7.0.2", "@mui/icons-material": "^7.0.2", "hono": "^4.7.7", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.28.0", "uuid": "^10.0.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241218.0", "@rsbuild/core": "^1.3.9", "@rsbuild/plugin-react": "^1.1.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "prettier": "^3.3.3", "typescript": "^5.6.3", "wrangler": "^4.36.0"}, "engines": {"node": ">=18.0.0"}}